{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/education/EducationClient.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Home)/education/EducationClient.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Home)/education/EducationClient.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+S,GAC5U,6EACA", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/education/EducationClient.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Home)/education/EducationClient.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Home)/education/EducationClient.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/utils/axiosInstance.js"], "sourcesContent": ["import axios from 'axios';\r\nimport Cookies from 'js-cookie';\r\n\r\nconst axiosInstance = axios.create({\r\n  baseURL: `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/`,\r\n  withCredentials: true,\r\n  headers: {\r\n    \"Accept\": \"application/json\"\r\n  },\r\n});\r\n\r\naxiosInstance.interceptors.request.use(\r\n  (config) => {\r\n    const token = Cookies.get(\"authToken\"); // Fetch latest token\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => Promise.reject(error)\r\n);\r\n\r\n// Response Interceptor: Handle Unauthorized Errors\r\naxiosInstance.interceptors.response.use(\r\n  (response) => response,  \r\n  (error) => {\r\n    if (error.response) {\r\n      const { status } = error.response;\r\n\r\n      if (status === 401) {\r\n        console.log('Unauthorized access. Redirecting to login...');\r\n        Cookies.remove(\"authToken\");  // Clear auth token\r\n        window.location.href = '/login'; // Redirect to login page\r\n      } else if (status === 404) {\r\n        console.error('Resource not found!');\r\n      } else if (status >= 500) {\r\n        console.error('Server error! Please try again later.');\r\n      }\r\n    } else {\r\n      console.error('Network error or request timeout.');\r\n    }\r\n\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default axiosInstance;\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,gBAAgB,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACjC,SAAS,6DAAwC,QAAQ,CAAC;IAC1D,iBAAiB;IACjB,SAAS;QACP,UAAU;IACZ;AACF;AAEA,cAAc,YAAY,CAAC,OAAO,CAAC,GAAG,CACpC,CAAC;IACC,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,cAAc,qBAAqB;IAC7D,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC,QAAU,QAAQ,MAAM,CAAC;AAG5B,mDAAmD;AACnD,cAAc,YAAY,CAAC,QAAQ,CAAC,GAAG,CACrC,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE;QAClB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ;QAEjC,IAAI,WAAW,KAAK;YAClB,QAAQ,GAAG,CAAC;YACZ,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC,cAAe,mBAAmB;YACjD,OAAO,QAAQ,CAAC,IAAI,GAAG,UAAU,yBAAyB;QAC5D,OAAO,IAAI,WAAW,KAAK;YACzB,QAAQ,KAAK,CAAC;QAChB,OAAO,IAAI,UAAU,KAAK;YACxB,QAAQ,KAAK,CAAC;QAChB;IACF,OAAO;QACL,QAAQ,KAAK,CAAC;IAChB;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/utils/apiUtils.js"], "sourcesContent": ["import axiosInstance from './axiosInstance';\r\nimport axios from 'axios';\r\n\r\n// GET Request Utility\r\nexport const get = async (url, params = {}) => {\r\n  try {\r\n    const response = await axiosInstance.get(url, { params });\r\n    return response.data;\r\n  } catch (error) {\r\n    if (error.name === 'AbortError' || axios.isCancel(error)) { // Handle both AbortError and Axios cancel\r\n      console.log('Request aborted:', url); // Optional quiet log\r\n      return null; // Or throw if needed\r\n    }\r\n    console.error('Error with GET request:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const getBrokers = async (url, params = {}) => {\r\n  try {\r\n    const response = await axiosInstance.get(url, { params });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error with GET request:', error);\r\n    throw error; // Re-throw for further handling\r\n  }\r\n};\r\n\r\n// POST Request Utility\r\nexport const post = async (url, data) => {\r\n  try {\r\n    const response = await axiosInstance.post(url, data);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error with POST request:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// PUT Request Utility\r\nexport const put = async (url, data) => {\r\n  try {\r\n    const response = await axiosInstance.put(url, data);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error with PUT request:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// DELETE Request Utility\r\nexport const deleteRequest = async (url) => {\r\n  try {\r\n    const response = await axiosInstance.delete(url);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error with DELETE request:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Username management utilities\r\nexport const updateUsername = async (username) => {\r\n  try {\r\n    const response = await axiosInstance.post('/username/update', { username });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error updating username:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const checkUsernameAvailability = async (username) => {\r\n  try {\r\n    const response = await axiosInstance.post('/username/check-availability', { username });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error checking username availability:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Two-Factor Authentication utilities\r\nexport const get2FAStatus = async () => {\r\n  try {\r\n    const response = await axiosInstance.get('/2fa/status');\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching 2FA status:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const enable2FA = async () => {\r\n  try {\r\n    const response = await axiosInstance.post('/2fa/enable');\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error enabling 2FA:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const disable2FA = async () => {\r\n  try {\r\n    const response = await axiosInstance.post('/2fa/disable');\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error disabling 2FA:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const generateRestoreCode = async () => {\r\n  try {\r\n    const response = await axiosInstance.post('/2fa/generate-restore-code');\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error generating restore code:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const update2FAAlwaysRequired = async (alwaysRequired) => {\r\n  try {\r\n    const response = await axiosInstance.post('/2fa/update-always-required', {\r\n      always_required: alwaysRequired\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error updating 2FA always required setting:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Secret Questions utilities\r\nexport const getSecretQuestions = async () => {\r\n  try {\r\n    const response = await axiosInstance.get('/account/secret-questions');\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching secret questions:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const saveSecretQuestions = async (questions) => {\r\n  try {\r\n    const response = await axiosInstance.post('/account/secret-questions', { questions });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error saving secret questions:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const updateSecretQuestions = async (questions) => {\r\n  try {\r\n    const response = await axiosInstance.put('/account/secret-questions', { questions });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error updating secret questions:', error);\r\n    throw error;\r\n  }\r\n};"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;;;AAGO,MAAM,MAAM,OAAO,KAAK,SAAS,CAAC,CAAC;IACxC,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,GAAG,CAAC,KAAK;YAAE;QAAO;QACvD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,IAAI,MAAM,IAAI,KAAK,gBAAgB,qIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,QAAQ;YACxD,QAAQ,GAAG,CAAC,oBAAoB,MAAM,qBAAqB;YAC3D,OAAO,MAAM,qBAAqB;QACpC;QACA,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAEO,MAAM,aAAa,OAAO,KAAK,SAAS,CAAC,CAAC;IAC/C,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,GAAG,CAAC,KAAK;YAAE;QAAO;QACvD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM,OAAO,gCAAgC;IAC/C;AACF;AAGO,MAAM,OAAO,OAAO,KAAK;IAC9B,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC,KAAK;QAC/C,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAGO,MAAM,MAAM,OAAO,KAAK;IAC7B,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,GAAG,CAAC,KAAK;QAC9C,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAGO,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,MAAM,CAAC;QAC5C,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAGO,MAAM,iBAAiB,OAAO;IACnC,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC,oBAAoB;YAAE;QAAS;QACzE,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,MAAM,4BAA4B,OAAO;IAC9C,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC,gCAAgC;YAAE;QAAS;QACrF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,MAAM;IACR;AACF;AAGO,MAAM,eAAe;IAC1B,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,GAAG,CAAC;QACzC,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAEO,MAAM,YAAY;IACvB,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC;QAC1C,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,MAAM;IACR;AACF;AAEO,MAAM,aAAa;IACxB,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC;QAC1C,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAEO,MAAM,sBAAsB;IACjC,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC;QAC1C,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,MAAM,0BAA0B,OAAO;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC,+BAA+B;YACvE,iBAAiB;QACnB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+CAA+C;QAC7D,MAAM;IACR;AACF;AAGO,MAAM,qBAAqB;IAChC,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,GAAG,CAAC;QACzC,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAEO,MAAM,sBAAsB,OAAO;IACxC,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,IAAI,CAAC,6BAA6B;YAAE;QAAU;QACnF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,MAAM,wBAAwB,OAAO;IAC1C,IAAI;QACF,MAAM,WAAW,MAAM,6HAAA,CAAA,UAAa,CAAC,GAAG,CAAC,6BAA6B;YAAE;QAAU;QAClF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 370, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 376, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/education/page.js"], "sourcesContent": ["import EducationClient from \"./EducationClient\";\r\nimport MetaHead from \"@/Seo/Meta/MetaHead\";\r\nimport { get } from \"@/utils/apiUtils\";\r\nimport { cookies } from 'next/headers';\r\n\r\n\r\nexport default async function EducationPage({ params, searchParams }) {\r\n  // const page = searchParams?.page || 1;\r\n  const page = parseInt(params.id) || 1;\r\n  // const key = searchParams?.key || '';\r\n  const cookieStore = cookies();\r\n  const key = cookieStore.get(\"educationSearchKey\")?.value || \"\";\r\n\r\n  const canonicalLink = page === 1\r\n  ? `https://www.tradereply.com/education`\r\n  : `https://www.tradereply.com/education/page/${page}`;\r\n\r\n  let educationArticles = [];\r\n  let educationPagination = {};\r\n\r\n  let nextLink = null;\r\n\r\n  try {\r\n    const response = await get(\"/article\", {\r\n      key,\r\n      type: \"education\",\r\n      page,\r\n      per_page: 25,\r\n    });\r\n\r\n    educationArticles = response.data.education;\r\n    educationPagination = response.data.meta;\r\n\r\n    if (educationPagination?.current_page < educationPagination?.total) {\r\n      nextLink = `https://www.tradereply.com/education/page/${educationPagination.current_page + 1}`;\r\n    }\r\n  \r\n    console.log(\"educationPagination!!!!!!\", educationPagination);\r\n\r\n\r\n  } catch (error) {\r\n    console.error(\"Failed to fetch education articles:\", error);\r\n  }\r\n   // SEO Meta handling\r\n   const isSearch = key?.trim() !== \"\";\r\n\r\n   const metaArray = {\r\n    title: \"TradeReply Education Center | Learn Trading Strategies\",\r\n    description: \"Learn the essentials of trading with TradeReply.com's Education Center. Access resources and tutorials on trading strategies, market analysis, and more.\",\r\n    og_title: \"TradeReply Education Center | Learn Trading Strategies\",\r\n    og_description: \"Learn the essentials of trading with TradeReply.com's Education Center. Access resources and tutorials on trading strategies, market analysis, and more.\",\r\n    og_site_name: \"TradeReply\",\r\n    twitter_title: \"TradeReply Education Center | Learn Trading Strategies\",\r\n    twitter_description: \"Learn the essentials of trading with TradeReply.com's Education Center. Access resources and tutorials on trading strategies, market analysis, and more.\",\r\n      noindex: isSearch,\r\n      ...(isSearch ? {} : {\r\n        canonical_link: canonicalLink,\r\n      }),\r\n      rel_next: nextLink,\r\n  };\r\n\r\n  return (\r\n    <>\r\n    <EducationClient\r\n      initialArticles={educationArticles}\r\n      initialPagination={educationPagination}\r\n      initialSearch={key}\r\n      initialPage={page}\r\n      metaArray={metaArray} \r\n    />\r\n  </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAGe,eAAe,cAAc,EAAE,MAAM,EAAE,YAAY,EAAE;IAClE,wCAAwC;IACxC,MAAM,OAAO,SAAS,OAAO,EAAE,KAAK;IACpC,uCAAuC;IACvC,MAAM,cAAc,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,MAAM,YAAY,GAAG,CAAC,uBAAuB,SAAS;IAE5D,MAAM,gBAAgB,SAAS,IAC7B,CAAC,oCAAoC,CAAC,GACtC,CAAC,0CAA0C,EAAE,MAAM;IAErD,IAAI,oBAAoB,EAAE;IAC1B,IAAI,sBAAsB,CAAC;IAE3B,IAAI,WAAW;IAEf,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,wHAAA,CAAA,MAAG,AAAD,EAAE,YAAY;YACrC;YACA,MAAM;YACN;YACA,UAAU;QACZ;QAEA,oBAAoB,SAAS,IAAI,CAAC,SAAS;QAC3C,sBAAsB,SAAS,IAAI,CAAC,IAAI;QAExC,IAAI,qBAAqB,eAAe,qBAAqB,OAAO;YAClE,WAAW,CAAC,0CAA0C,EAAE,oBAAoB,YAAY,GAAG,GAAG;QAChG;QAEA,QAAQ,GAAG,CAAC,6BAA6B;IAG3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;IACvD;IACC,oBAAoB;IACpB,MAAM,WAAW,KAAK,WAAW;IAEjC,MAAM,YAAY;QACjB,OAAO;QACP,aAAa;QACb,UAAU;QACV,gBAAgB;QAChB,cAAc;QACd,eAAe;QACf,qBAAqB;QACnB,SAAS;QACT,GAAI,WAAW,CAAC,IAAI;YAClB,gBAAgB;QAClB,CAAC;QACD,UAAU;IACd;IAEA,qBACE;kBACA,cAAA,8OAAC,sJAAA,CAAA,UAAe;YACd,iBAAiB;YACjB,mBAAmB;YACnB,eAAe;YACf,aAAa;YACb,WAAW;;;;;;;AAIjB", "debugId": null}}, {"offset": {"line": 445, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}