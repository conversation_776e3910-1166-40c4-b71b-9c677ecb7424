"use client";
import TextInput from "@/Components/UI/TextInput";
import AuthLayout from "@/Layouts/AuthLayout";
import { useState, useRef, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { createUsername } from "@/utils/auth";
import InputError from "@/Components/UI/InputError";
import Link from "next/link";
import CommonButton from "@/Components/UI/CommonButton";
import { createUsernameSchema } from "@/validations/schema";
import { Formik, Field, Form } from "formik";
import LoginFooter from "@/Components/UI/LoginFooter";
import MetaHead from "@/Seo/Meta/MetaHead";
import AuthLogo from "@/Components/common/AuthLogo";
import debounce from "lodash.debounce";
import { CheckIcon } from "@/assets/svgIcons/SvgIcon";
import { useSearchParams } from "next/navigation";
import Cookies from "js-cookie";

const initialValues = {
    username: "",
};

export default function CreateUsername({ status }) {
    const router = useRouter();

    const searchParams = useSearchParams();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [errorMessages, setErrorMessages] = useState("");
    const [errorBoolean, setErrorBoolean] = useState(false);
    const [isSessionValid, setIsSessionValid] = useState(null);
    const expiredOnceRef = useRef(false);
    const [agreeToTerms, setAgreeToTerms] = useState(false);
    const [isUsernameValid, setIsUsernameValid] = useState(false);

    const provider = searchParams.get("provider");

    const dataType = provider ? `signup_${provider}` : "signup_data";
    useEffect(() => {
        const checkSessionValidity = () => {
            const savedData = JSON.parse(sessionStorage.getItem(dataType));

            if ((!savedData || !savedData.uuid || !savedData.expiresAt) && !expiredOnceRef.current) {
                if (!expiredOnceRef.current) {
                    expiredOnceRef.current = true;
                    router.replace("/login");
                }
                return false;
            }

            const expired = Date.now() > savedData.expiresAt;

            if (expired && !expiredOnceRef.current) {
                expiredOnceRef.current = true;
                sessionStorage.removeItem(dataType);
                sessionStorage.removeItem("masked_email");
                sessionStorage.removeItem("identifier_type");
                sessionStorage.setItem("sessionExpired", "true");
                router.replace("/login");
                return false;
            }

            return true;
        };

        if (checkSessionValidity()) {
            setIsSessionValid(true);
            const interval = setInterval(checkSessionValidity, 5000);
            return () => clearInterval(interval);
        }
    }, [dataType]);


    const handleSubmit = async (values) => {
        try {
            if (!values?.username) {
                setErrorBoolean(true);
                setErrorMessages("Please enter your username.");
                return;
            }

            setIsSubmitting(true);
            const savedData = JSON.parse(sessionStorage.getItem(dataType));

            if (!savedData || !savedData.uuid) {
                setIsSubmitting(false);
                setErrorBoolean(true);
                setErrorMessages("Session expired. Please start again.");
                return;
            }

            const payload = {
                username: values.username,
                uuid: savedData.uuid,
                type: dataType,
                flag: false, // optional but clearer
            };

            const response = await createUsername(payload);
            console.log("response", response);

            if (response?.success) {
                Cookies.set("authToken", response.data.token);
                sessionStorage.removeItem(dataType);

                if (response?.data?.plan === 'free') {
                    Cookies.set("first_login", "true");
                    router.push("/checkout")
                } else {
                    router.push("/dashboard?source=signup_member_login_default");
                }
            } else {
                setErrorBoolean(true);
                setErrorMessages(
                    response?.error?.response?.data?.message || response?.message || "Something went wrong. Please try again."
                );
            }
        } catch (error) {
            setErrorBoolean(true);
            setErrorMessages(
                error?.response?.data?.message || error?.message || "An unexpected error occurred."
            );
        } finally {
            setIsSubmitting(false);
        }
    };


    const checkUsernameAvailability = useCallback(
        debounce(async (username) => {
            const savedData = JSON.parse(sessionStorage.getItem(dataType));

            if (!savedData || !savedData.uuid) {
                setIsSubmitting(false);
                return;
            }

            try {
                const payload = {
                    username,
                    uuid: savedData.uuid,
                    type: dataType,
                    flag: true, // for uniqueness check
                };

                const response = await createUsername(payload);

                if (!response?.success) {
                    const errorData = response?.error?.response?.data;
                    setErrorBoolean(true);
                    setIsUsernameValid(false);
                    setErrorMessages(errorData?.message || "Username unavailable.");

                    if (errorData?.errorData?.type === "token_expired") {
                        sessionStorage.removeItem(dataType);
                        router.push("/signup");
                    }
                }
                else {
                    setIsUsernameValid(true);
                }
            } catch (error) {
                setErrorBoolean(true);
                setErrorMessages("Error checking username availability.");
            }
        }, 500),
        [dataType]
    );

    const routes = [
        'auth-email', 'change-password', 'create-username', 'locate-account', 'login',
        'security-check', 'signup', 'accessibility', 'advertising', 'blog', 'blog-sitemap',
        'brand-assets', 'brokers', 'cart', 'category', 'checkout', 'checkout/thank-you',
        'cookies', 'disclaimer', 'education', 'features', 'marketplace', 'marketplace',
        'partner', 'partner-rules', 'pricing', 'privacy', 'refer-a-friend', 'search',
        'sitemap', 'status', 'terms', 'trading-calculatormp', 'account/connections',
        '/account/details', '/account/overview', '/account/payments', '/account/privacy',
        '/account/public-profile', '/account/security', '/account/subscriptions',
        '/account/transactions', '/dashboard', '/trading-calculator', '/dashboard/portfolio-manager',
        '/dashboard/strategy-builder', '/dashboard/strategy-manager', '/dashboard/tag-manager',
        '/dashboard/tag_manager', '/dashboard/trade-analysis', '/dashboard/trade-builder',
        '/dashboard/trade-importer', '/dashboard/trade-manager', '/dashboard/trading-calculator',
        '/not-found', '/super-admin/blogs', '/super-admin/category', '/super-admin/dashboard',
        '/super-admin/education'
    ];

    const generateVariants = (route) => {
        const routeWithoutSlash = route.replace(/^\/?/, '');
        const variant = routeWithoutSlash.replace(/-/g, '_');
        return [routeWithoutSlash, variant];
    };

    const allRoutesWithVariants = routes.flatMap(route => generateVariants(route));

    const reservedUsernames = [
        'admin', 'support', 'help', 'info', 'root', 'system', 'test',
        'username', 'null', 'user', 'guest',
        ...allRoutesWithVariants, // Add all routes with variants
    ];

    const validateUsernameLocally = (username) => {
        const value = username.toLowerCase();

        if (reservedUsernames.includes(value)) {
            return 'This username is reserved and cannot be used.';
        }

        if (
            !/^[a-z0-9_]+$/.test(value) ||
            value.length < 3 || value.length > 20 ||
            (value.match(/_/g) || []).length > 2 ||
            value.includes('__') ||
            value.startsWith('_') ||
            /^\d+$/.test(value) ||
            /(\d)\1{2,}/.test(value) ||
            /123456|234567|345678|456789|987654|876543/.test(value)
        ) {
            return 'Usernames must be 3–20 characters, include at least one letter, and may only contain letters, numbers, and underscores (no consecutive underscores).';
        }
        if (/^(.)\1{4,}$/.test(value)) {
            return 'Username cannot consist of a single character repeated too many times.';
        }

        return null;
    };

    if (isSessionValid === null) {
        return <div className="loading-screen">Checking session...</div>;
    }

    if (isSessionValid === false) {
        return null;
    }
    return (
        <AuthLayout>
            <MetaHead
                props={{
                    noindex: true,
                    title: "Create Your TradeReply Username | Secure Your Trading Identity",
                }}
            />
            <div className="loginCommon_rightSide">
                <div className="loginCommon_rightSide_inner">
                    <div className="loginCommon_rightSide_formBox">
                        <AuthLogo />
                        <div className="loginHeading">
                            <h1>You’re So Close!</h1>
                        </div>
                        <div className="text-center pt-3 pb-4">
                            <span>Finish setting up your TradeReply account.</span>
                        </div>
                        <Formik
                            initialValues={initialValues}
                            validationSchema={createUsernameSchema}
                        >
                            {({ handleChange, values, submitCount }) => (
                                <Form
                                    onKeyDown={(e) => {
                                        if (
                                            e.key === 'Enter' &&
                                            (isSubmitting || errorMessages)
                                        ) {
                                            e.preventDefault();
                                        }
                                    }}
                                >
                                    <p className="inputLabel">Public Username - you can change it twice</p>
                                    <div className="authCorrectIcon">
                                        <div className="checkIcon">
                                            {values.username && !errorMessages && values.username.length >= 3 && <CheckIcon />}
                                        </div>
                                        <Field name="username">
                                            {({ field, form }) => (
                                                <TextInput
                                                    {...field}
                                                    placeholder="Enter your username"
                                                    type="text"
                                                    maxLength={20}
                                                    usernameInput="usernameInput"
                                                    error={errorBoolean && !field.value ? null : <InputError message={errorMessages} />}
                                                    isError={errorBoolean && field.value.length > 0}

                                                    onChange={(e) => {
                                                        const value = e.target.value.trim();
                                                        handleChange(e);

                                                        setErrorMessages(null);
                                                        setErrorBoolean(false);
                                                        form.setFieldError('username', '');

                                                        if (value === '') {
                                                            return;
                                                        }

                                                        if (value.length < 3 || value.length > 20) {
                                                            const msg = 'Usernames must be 3–20 characters, include at least one letter, and may only contain letters, numbers, and underscores (no consecutive underscores).';
                                                            setErrorMessages(msg);
                                                            setErrorBoolean(true);
                                                            form.setFieldError('username', msg);
                                                            return;
                                                        }

                                                        const localValidationError = validateUsernameLocally(value);
                                                        if (localValidationError) {
                                                            setErrorMessages(localValidationError);
                                                            setErrorBoolean(true);
                                                            setIsUsernameValid(false);
                                                            form.setFieldError('username', localValidationError);
                                                            return;
                                                        }

                                                        if (value.length >= 3 && value.length <= 20) {
                                                            checkUsernameAvailability(value);
                                                            setIsUsernameValid(true);
                                                        }
                                                    }}
                                                />
                                            )}
                                        </Field>


                                    </div>
                                    <div className="custom_checkbox">
                                        <input
                                            className="custom_checkbox_input form-check-input"
                                            type="checkbox"
                                            id="marketingEmail"
                                        />
                                        <label className="custom_checkbox_label" htmlFor="marketingEmail">
                                            Receive marketing emails and offers
                                        </label>
                                    </div>
                                    <div className="custom_checkbox">
                                        <input
                                            className="custom_checkbox_input form-check-input"
                                            type="checkbox"
                                            checked={agreeToTerms}
                                            onChange={(e) => setAgreeToTerms(e.target.checked)}
                                            id="termsAndConditions"
                                        />                                        <label className="custom_checkbox_label" htmlFor="termsAndConditions">
                                            I agree to the
                                            <a href="https://dev.tradereply.com/terms" target="_blank" className="mx-1">
                                                Terms of Use
                                            </a>
                                            and
                                            <a href="https://dev.tradereply.com/privacy" target="_blank" className="mx-1">
                                                Privacy Policy
                                            </a>
                                        </label>
                                    </div>
                                    <CommonButton
                                        type="button"
                                        title={isSubmitting ? "Loading" : "Complete Account"}
                                        fluid
                                        disabled={
                                            isSubmitting ||
                                            errorMessages ||
                                            !agreeToTerms ||
                                            !isUsernameValid
                                        } onClick={() => handleSubmit(values)}
                                    />
                                    <Link href={"/login"} className="w-100 mt-3">
                                        <CommonButton
                                            type="submit"
                                            title="Cancel Account Creation"
                                            white20
                                            disabled={isSubmitting}
                                        />
                                    </Link>
                                </Form>
                            )}
                        </Formik>
                    </div>
                    <div className="mt-4 mt-md-5">
                        <LoginFooter />
                    </div>
                </div>
            </div>
        </AuthLayout>
    );
}