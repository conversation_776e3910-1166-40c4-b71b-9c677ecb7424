{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/category/CategoryClient.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Home)/category/CategoryClient.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Home)/category/CategoryClient.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6S,GAC1U,2EACA", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/category/CategoryClient.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Home)/category/CategoryClient.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Home)/category/CategoryClient.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyR,GACtT,uDACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Seo/Schema/JsonLdSchema.js"], "sourcesContent": ["/**\n * JsonLdSchema Component\n * \n * Renders JSON-LD structured data schemas for SEO purposes.\n * Each schema is rendered in its own separate <script type=\"application/ld+json\"> tag\n * to ensure proper search engine crawling and indexing.\n * \n * Usage:\n * <JsonLdSchema schemas={[organizationSchema, websiteSchema]} />\n */\n\nexport default function JsonLdSchema({ schemas = [] }) {\n  if (!schemas || schemas.length === 0) {\n    return null;\n  }\n\n  return (\n    <>\n      {schemas.map((schema, index) => (\n        <script\n          key={index}\n          type=\"application/ld+json\"\n          dangerouslySetInnerHTML={{\n            __html: JSON.stringify(schema, null, 0)\n          }}\n        />\n      ))}\n    </>\n  );\n}\n\n/**\n * Homepage Schema Generators\n */\n\nexport const generateOrganizationSchema = () => {\n  return {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"Organization\",\n    \"name\": \"TradeReply\",\n    \"url\": \"https://www.tradereply.com\",\n    \"logo\": \"https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png\",\n    \"contactPoint\": {\n      \"@type\": \"ContactPoint\",\n      \"url\": \"https://www.tradereply.com/help\",\n      \"contactType\": \"Customer Support\",\n      \"areaServed\": \"Global\",\n      \"availableLanguage\": \"English\"\n    },\n    \"sameAs\": [\n      \"https://www.facebook.com/TradeReply\",\n      \"https://www.instagram.com/tradereply\",\n      \"https://x.com/JoinTradeReply\"\n    ]\n  };\n};\n\nexport const generateWebsiteSchema = () => {\n  return {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"WebSite\",\n    \"url\": \"https://www.tradereply.com/\",\n    \"name\": \"TradeReply\"\n  };\n};\n\n/**\n * Blog Article Schema Generator\n */\n\nexport const generateBlogPostingSchema = ({\n  canonicalUrl,\n  headline,\n  description,\n  imageUrl,\n  datePublished,\n  dateModified,\n  articleBody,\n  keywords\n}) => {\n  // Only generate schema if required fields are present\n  if (!canonicalUrl || !headline) {\n    return null;\n  }\n\n  return {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"BlogPosting\",\n    \"mainEntityOfPage\": {\n      \"@type\": \"WebPage\",\n      \"@id\": canonicalUrl\n    },\n    \"headline\": headline,\n    \"description\": description || \"\",\n    \"image\": imageUrl || \"\",\n    \"author\": {\n      \"@type\": \"Organization\",\n      \"name\": \"TradeReply\"\n    },\n    \"publisher\": {\n      \"@type\": \"Organization\",\n      \"name\": \"TradeReply\",\n      \"logo\": {\n        \"@type\": \"ImageObject\",\n        \"url\": \"https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png\"\n      }\n    },\n    \"datePublished\": datePublished || \"\",\n    \"dateModified\": dateModified || datePublished || \"\",\n    \"articleBody\": articleBody || description || \"\",\n    \"keywords\": keywords || \"\"\n  };\n};\n\n/**\n * Utility function to format dates to ISO 8601 format\n * Converts various date formats to ISO 8601 string format required by schema.org\n * \n * @param {string|Date} date - Date to format\n * @returns {string|null} - ISO 8601 formatted date string or null if invalid\n */\nexport const formatDateToISO = (date) => {\n  if (!date) return null;\n  \n  try {\n    // Handle different date formats\n    let dateObj;\n    if (typeof date === 'string') {\n      dateObj = new Date(date);\n    } else if (date instanceof Date) {\n      dateObj = date;\n    } else {\n      return null;\n    }\n    \n    // Check if date is valid\n    if (isNaN(dateObj.getTime())) {\n      return null;\n    }\n    \n    return dateObj.toISOString();\n  } catch (error) {\n    console.warn('Error formatting date to ISO:', error);\n    return null;\n  }\n};\n\n/**\n * Utility function to safely extract blog slug from URL or data\n * \n * @param {Object} blog - Blog data object\n * @returns {string} - Clean blog slug\n */\nexport const getBlogSlug = (blog) => {\n  if (!blog) return '';\n  \n  // If slug exists, use it directly\n  if (blog.slug) {\n    return blog.slug;\n  }\n  \n  // Fallback: generate slug from title\n  if (blog.title) {\n    return blog.title\n      .toLowerCase()\n      .replace(/[^a-z0-9]+/g, '-')\n      .replace(/^-+|-+$/g, '');\n  }\n  \n  return '';\n};\n\n/**\n * Utility function to validate and clean keywords string\n *\n * @param {string} keywords - Comma-separated keywords\n * @returns {string} - Cleaned keywords string\n */\nexport const cleanKeywords = (keywords) => {\n  if (!keywords || typeof keywords !== 'string') {\n    return '';\n  }\n\n  return keywords\n    .split(',')\n    .map(keyword => keyword.trim())\n    .filter(keyword => keyword.length > 0)\n    .join(', ');\n};\n\n/**\n * Marketplace Product Schema Generator\n */\n\nexport const generateProductSchema = ({\n  name,\n  description,\n  image,\n  brand,\n  price,\n  currency = \"USD\",\n  availability = \"http://schema.org/InStock\",\n  url,\n  seller,\n  aggregateRating,\n  reviews = []\n}) => {\n  // Only generate schema if required fields are present\n  if (!name || !price) {\n    return null;\n  }\n\n  const schema = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"Product\",\n    \"name\": name,\n    \"description\": description || \"\",\n    \"image\": image || \"\",\n    \"offers\": {\n      \"@type\": \"Offer\",\n      \"price\": price.toString(),\n      \"priceCurrency\": currency,\n      \"availability\": availability,\n      \"url\": url || \"\"\n    }\n  };\n\n  // Add brand if provided\n  if (brand) {\n    schema.brand = {\n      \"@type\": \"Brand\",\n      \"name\": brand\n    };\n  }\n\n  // Add seller if provided\n  if (seller) {\n    schema.offers.seller = {\n      \"@type\": \"Organization\",\n      \"name\": seller.name || \"\",\n      \"url\": seller.url || \"\"\n    };\n  }\n\n  // Add aggregate rating if provided\n  if (aggregateRating && aggregateRating.ratingValue && aggregateRating.reviewCount) {\n    schema.aggregateRating = {\n      \"@type\": \"AggregateRating\",\n      \"ratingValue\": aggregateRating.ratingValue.toString(),\n      \"reviewCount\": aggregateRating.reviewCount.toString()\n    };\n  }\n\n  // Add reviews if provided (maximum 3)\n  if (reviews && reviews.length > 0) {\n    schema.review = reviews.slice(0, 3).map(review => ({\n      \"@type\": \"Review\",\n      \"author\": {\n        \"@type\": \"Person\",\n        \"name\": review.author || \"Anonymous\"\n      },\n      \"datePublished\": formatDateToISO(review.datePublished) || \"\",\n      \"reviewBody\": review.reviewBody || \"\",\n      \"reviewRating\": {\n        \"@type\": \"Rating\",\n        \"ratingValue\": review.rating ? review.rating.toString() : \"5\"\n      }\n    }));\n  }\n\n  return schema;\n};\n\n/**\n * Category Page Schema Generators\n */\n\nexport const generateCollectionPageSchema = ({\n  name,\n  description,\n  url,\n  articles = [],\n  currentPage = 1\n}) => {\n  // Only generate schema if required fields are present\n  if (!name || !url) {\n    return null;\n  }\n\n  const pageTitle = currentPage > 1 ? `${name} – Page ${currentPage}` : name;\n\n  const schema = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"CollectionPage\",\n    \"name\": pageTitle,\n    \"description\": description || \"\",\n    \"url\": url\n  };\n\n  // Add articles as ListItem elements (maximum 10)\n  if (articles && articles.length > 0) {\n    schema.mainEntity = {\n      \"@type\": \"ItemList\",\n      \"numberOfItems\": articles.length,\n      \"itemListElement\": articles.slice(0, 10).map((article, index) => ({\n        \"@type\": \"ListItem\",\n        \"position\": index + 1,\n        \"item\": {\n          \"@type\": article.type === 'blog' ? \"BlogPosting\" : \"Article\",\n          \"@id\": `https://www.tradereply.com/${article.type}/${article.slug}`,\n          \"name\": article.title || \"\",\n          \"description\": article.summary || \"\",\n          \"datePublished\": formatDateToISO(article.created_at) || \"\",\n          \"author\": {\n            \"@type\": \"Organization\",\n            \"name\": \"TradeReply\"\n          }\n        }\n      }))\n    };\n  }\n\n  return schema;\n};\n\nexport const generateBreadcrumbListSchema = ({\n  items = []\n}) => {\n  // Only generate schema if items are provided\n  if (!items || items.length === 0) {\n    return null;\n  }\n\n  return {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"BreadcrumbList\",\n    \"itemListElement\": items.map((item, index) => ({\n      \"@type\": \"ListItem\",\n      \"position\": index + 1,\n      \"name\": item.name,\n      \"item\": item.url\n    }))\n  };\n};\n\n/**\n * Utility Functions for Schema Generation\n */\n\n/**\n * Select reviews based on average rating logic\n *\n * @param {Array} allReviews - All available reviews\n * @param {number} averageRating - Average rating (e.g., 4.2)\n * @param {number} maxReviews - Maximum number of reviews to select (default: 3)\n * @returns {Array} - Selected reviews\n */\nexport const selectReviewsForSchema = (allReviews = [], averageRating = 5, maxReviews = 3) => {\n  if (!allReviews || allReviews.length === 0) {\n    return [];\n  }\n\n  // Round average rating to nearest integer for selection logic\n  const targetRating = Math.round(averageRating);\n\n  // Filter reviews by target rating\n  const targetReviews = allReviews.filter(review =>\n    Math.round(parseFloat(review.rating || 5)) === targetRating\n  );\n\n  // If we have enough reviews of the target rating, use them\n  if (targetReviews.length >= maxReviews) {\n    return shuffleArray(targetReviews).slice(0, maxReviews);\n  }\n\n  // If not enough target reviews, include nearby ratings\n  const nearbyRatings = [targetRating, targetRating - 1, targetRating + 1].filter(r => r >= 1 && r <= 5);\n  const nearbyReviews = allReviews.filter(review =>\n    nearbyRatings.includes(Math.round(parseFloat(review.rating || 5)))\n  );\n\n  return shuffleArray(nearbyReviews).slice(0, maxReviews);\n};\n\n/**\n * Shuffle array utility function\n *\n * @param {Array} array - Array to shuffle\n * @returns {Array} - Shuffled array\n */\nexport const shuffleArray = (array) => {\n  const shuffled = [...array];\n  for (let i = shuffled.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1));\n    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];\n  }\n  return shuffled;\n};\n\n/**\n * Generate breadcrumb items for category pages\n *\n * @param {string} categoryName - Category name\n * @param {number} currentPage - Current page number (optional)\n * @returns {Array} - Breadcrumb items\n */\nexport const generateCategoryBreadcrumbs = (categoryName = \"All Articles\", currentPage = null) => {\n  const breadcrumbs = [\n    {\n      name: \"Home\",\n      url: \"https://www.tradereply.com/\"\n    },\n    {\n      name: categoryName,\n      url: \"https://www.tradereply.com/category\"\n    }\n  ];\n\n  // Add page breadcrumb for paginated pages\n  if (currentPage && currentPage > 1) {\n    breadcrumbs.push({\n      name: `Page ${currentPage}`,\n      url: `https://www.tradereply.com/category/page/${currentPage}`\n    });\n  }\n\n  return breadcrumbs;\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC;;;;;;;;;;;;;;;;;AAEc,SAAS,aAAa,EAAE,UAAU,EAAE,EAAE;IACnD,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,qBACE;kBACG,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;gBAEC,MAAK;gBACL,yBAAyB;oBACvB,QAAQ,KAAK,SAAS,CAAC,QAAQ,MAAM;gBACvC;eAJK;;;;;;AASf;AAMO,MAAM,6BAA6B;IACxC,OAAO;QACL,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,gBAAgB;YACd,SAAS;YACT,OAAO;YACP,eAAe;YACf,cAAc;YACd,qBAAqB;QACvB;QACA,UAAU;YACR;YACA;YACA;SACD;IACH;AACF;AAEO,MAAM,wBAAwB;IACnC,OAAO;QACL,YAAY;QACZ,SAAS;QACT,OAAO;QACP,QAAQ;IACV;AACF;AAMO,MAAM,4BAA4B,CAAC,EACxC,YAAY,EACZ,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,aAAa,EACb,YAAY,EACZ,WAAW,EACX,QAAQ,EACT;IACC,sDAAsD;IACtD,IAAI,CAAC,gBAAgB,CAAC,UAAU;QAC9B,OAAO;IACT;IAEA,OAAO;QACL,YAAY;QACZ,SAAS;QACT,oBAAoB;YAClB,SAAS;YACT,OAAO;QACT;QACA,YAAY;QACZ,eAAe,eAAe;QAC9B,SAAS,YAAY;QACrB,UAAU;YACR,SAAS;YACT,QAAQ;QACV;QACA,aAAa;YACX,SAAS;YACT,QAAQ;YACR,QAAQ;gBACN,SAAS;gBACT,OAAO;YACT;QACF;QACA,iBAAiB,iBAAiB;QAClC,gBAAgB,gBAAgB,iBAAiB;QACjD,eAAe,eAAe,eAAe;QAC7C,YAAY,YAAY;IAC1B;AACF;AASO,MAAM,kBAAkB,CAAC;IAC9B,IAAI,CAAC,MAAM,OAAO;IAElB,IAAI;QACF,gCAAgC;QAChC,IAAI;QACJ,IAAI,OAAO,SAAS,UAAU;YAC5B,UAAU,IAAI,KAAK;QACrB,OAAO,IAAI,gBAAgB,MAAM;YAC/B,UAAU;QACZ,OAAO;YACL,OAAO;QACT;QAEA,yBAAyB;QACzB,IAAI,MAAM,QAAQ,OAAO,KAAK;YAC5B,OAAO;QACT;QAEA,OAAO,QAAQ,WAAW;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,iCAAiC;QAC9C,OAAO;IACT;AACF;AAQO,MAAM,cAAc,CAAC;IAC1B,IAAI,CAAC,MAAM,OAAO;IAElB,kCAAkC;IAClC,IAAI,KAAK,IAAI,EAAE;QACb,OAAO,KAAK,IAAI;IAClB;IAEA,qCAAqC;IACrC,IAAI,KAAK,KAAK,EAAE;QACd,OAAO,KAAK,KAAK,CACd,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,YAAY;IACzB;IAEA,OAAO;AACT;AAQO,MAAM,gBAAgB,CAAC;IAC5B,IAAI,CAAC,YAAY,OAAO,aAAa,UAAU;QAC7C,OAAO;IACT;IAEA,OAAO,SACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,UAAW,QAAQ,IAAI,IAC3B,MAAM,CAAC,CAAA,UAAW,QAAQ,MAAM,GAAG,GACnC,IAAI,CAAC;AACV;AAMO,MAAM,wBAAwB,CAAC,EACpC,IAAI,EACJ,WAAW,EACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,WAAW,KAAK,EAChB,eAAe,2BAA2B,EAC1C,GAAG,EACH,MAAM,EACN,eAAe,EACf,UAAU,EAAE,EACb;IACC,sDAAsD;IACtD,IAAI,CAAC,QAAQ,CAAC,OAAO;QACnB,OAAO;IACT;IAEA,MAAM,SAAS;QACb,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,eAAe,eAAe;QAC9B,SAAS,SAAS;QAClB,UAAU;YACR,SAAS;YACT,SAAS,MAAM,QAAQ;YACvB,iBAAiB;YACjB,gBAAgB;YAChB,OAAO,OAAO;QAChB;IACF;IAEA,wBAAwB;IACxB,IAAI,OAAO;QACT,OAAO,KAAK,GAAG;YACb,SAAS;YACT,QAAQ;QACV;IACF;IAEA,yBAAyB;IACzB,IAAI,QAAQ;QACV,OAAO,MAAM,CAAC,MAAM,GAAG;YACrB,SAAS;YACT,QAAQ,OAAO,IAAI,IAAI;YACvB,OAAO,OAAO,GAAG,IAAI;QACvB;IACF;IAEA,mCAAmC;IACnC,IAAI,mBAAmB,gBAAgB,WAAW,IAAI,gBAAgB,WAAW,EAAE;QACjF,OAAO,eAAe,GAAG;YACvB,SAAS;YACT,eAAe,gBAAgB,WAAW,CAAC,QAAQ;YACnD,eAAe,gBAAgB,WAAW,CAAC,QAAQ;QACrD;IACF;IAEA,sCAAsC;IACtC,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;QACjC,OAAO,MAAM,GAAG,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,SAAU,CAAC;gBACjD,SAAS;gBACT,UAAU;oBACR,SAAS;oBACT,QAAQ,OAAO,MAAM,IAAI;gBAC3B;gBACA,iBAAiB,gBAAgB,OAAO,aAAa,KAAK;gBAC1D,cAAc,OAAO,UAAU,IAAI;gBACnC,gBAAgB;oBACd,SAAS;oBACT,eAAe,OAAO,MAAM,GAAG,OAAO,MAAM,CAAC,QAAQ,KAAK;gBAC5D;YACF,CAAC;IACH;IAEA,OAAO;AACT;AAMO,MAAM,+BAA+B,CAAC,EAC3C,IAAI,EACJ,WAAW,EACX,GAAG,EACH,WAAW,EAAE,EACb,cAAc,CAAC,EAChB;IACC,sDAAsD;IACtD,IAAI,CAAC,QAAQ,CAAC,KAAK;QACjB,OAAO;IACT;IAEA,MAAM,YAAY,cAAc,IAAI,GAAG,KAAK,QAAQ,EAAE,aAAa,GAAG;IAEtE,MAAM,SAAS;QACb,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,eAAe,eAAe;QAC9B,OAAO;IACT;IAEA,iDAAiD;IACjD,IAAI,YAAY,SAAS,MAAM,GAAG,GAAG;QACnC,OAAO,UAAU,GAAG;YAClB,SAAS;YACT,iBAAiB,SAAS,MAAM;YAChC,mBAAmB,SAAS,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,SAAS,QAAU,CAAC;oBAChE,SAAS;oBACT,YAAY,QAAQ;oBACpB,QAAQ;wBACN,SAAS,QAAQ,IAAI,KAAK,SAAS,gBAAgB;wBACnD,OAAO,CAAC,2BAA2B,EAAE,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,IAAI,EAAE;wBACnE,QAAQ,QAAQ,KAAK,IAAI;wBACzB,eAAe,QAAQ,OAAO,IAAI;wBAClC,iBAAiB,gBAAgB,QAAQ,UAAU,KAAK;wBACxD,UAAU;4BACR,SAAS;4BACT,QAAQ;wBACV;oBACF;gBACF,CAAC;QACH;IACF;IAEA,OAAO;AACT;AAEO,MAAM,+BAA+B,CAAC,EAC3C,QAAQ,EAAE,EACX;IACC,6CAA6C;IAC7C,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAChC,OAAO;IACT;IAEA,OAAO;QACL,YAAY;QACZ,SAAS;QACT,mBAAmB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7C,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ,KAAK,IAAI;gBACjB,QAAQ,KAAK,GAAG;YAClB,CAAC;IACH;AACF;AAcO,MAAM,yBAAyB,CAAC,aAAa,EAAE,EAAE,gBAAgB,CAAC,EAAE,aAAa,CAAC;IACvF,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG;QAC1C,OAAO,EAAE;IACX;IAEA,8DAA8D;IAC9D,MAAM,eAAe,KAAK,KAAK,CAAC;IAEhC,kCAAkC;IAClC,MAAM,gBAAgB,WAAW,MAAM,CAAC,CAAA,SACtC,KAAK,KAAK,CAAC,WAAW,OAAO,MAAM,IAAI,QAAQ;IAGjD,2DAA2D;IAC3D,IAAI,cAAc,MAAM,IAAI,YAAY;QACtC,OAAO,aAAa,eAAe,KAAK,CAAC,GAAG;IAC9C;IAEA,uDAAuD;IACvD,MAAM,gBAAgB;QAAC;QAAc,eAAe;QAAG,eAAe;KAAE,CAAC,MAAM,CAAC,CAAA,IAAK,KAAK,KAAK,KAAK;IACpG,MAAM,gBAAgB,WAAW,MAAM,CAAC,CAAA,SACtC,cAAc,QAAQ,CAAC,KAAK,KAAK,CAAC,WAAW,OAAO,MAAM,IAAI;IAGhE,OAAO,aAAa,eAAe,KAAK,CAAC,GAAG;AAC9C;AAQO,MAAM,eAAe,CAAC;IAC3B,MAAM,WAAW;WAAI;KAAM;IAC3B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;QAC5C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;QAC3C,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG;YAAC,QAAQ,CAAC,EAAE;YAAE,QAAQ,CAAC,EAAE;SAAC;IACzD;IACA,OAAO;AACT;AASO,MAAM,8BAA8B,CAAC,eAAe,cAAc,EAAE,cAAc,IAAI;IAC3F,MAAM,cAAc;QAClB;YACE,MAAM;YACN,KAAK;QACP;QACA;YACE,MAAM;YACN,KAAK;QACP;KACD;IAED,0CAA0C;IAC1C,IAAI,eAAe,cAAc,GAAG;QAClC,YAAY,IAAI,CAAC;YACf,MAAM,CAAC,KAAK,EAAE,aAAa;YAC3B,KAAK,CAAC,yCAAyC,EAAE,aAAa;QAChE;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/category/page.js"], "sourcesContent": ["import CategoryClient from \"./CategoryClient\";\r\nimport { cookies } from \"next/headers\";\r\nimport JsonLdSchema, { generateCollectionPageSchema, generateBreadcrumbListSchema, generateCategoryBreadcrumbs } from \"@/Seo/Schema/JsonLdSchema\";\r\n\r\nexport default async function CategoryPage({ params, searchParams }) {\r\n  const slug = searchParams?.slug || null;\r\n  const page = parseInt(params?.id) || 1;\r\n  const cookieStore = cookies();\r\n  const key = cookieStore.get(\"categorySearchKey\")?.value || \"\";\r\n\r\n  const canonicalLink =\r\n    page === 1\r\n      ? `https://www.tradereply.com/category`\r\n      : `https://www.tradereply.com/category/page/${page}`;\r\n\r\n  let data = {\r\n    allcategories: [],\r\n    articles: [],\r\n    meta: {},\r\n    selected_category: null,\r\n  };\r\n\r\n  let categoryPagination = {};\r\n  let nextLink = null;\r\n\r\n  try {\r\n    const query = new URLSearchParams({ slug, key, page }).toString();\r\n    const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || '';\r\n    const res = await fetch(`${apiBase}/category?${query}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (!res.ok) {\r\n      throw new Error(\"Failed to fetch category data\");\r\n    }\r\n\r\n    const response = await res.json();\r\n    data = response.data;\r\n    console.log(\"API RESPONSE (server):\", JSON.stringify(response?.data, null, 2));\r\n\r\n    categoryPagination = data.meta;\r\n    if (categoryPagination?.current_page < categoryPagination?.total) {\r\n      nextLink = `https://www.tradereply.com/category/page/${categoryPagination.current_page + 1}`;\r\n    }\r\n\r\n  } catch (error) {\r\n    console.error(\"Failed to fetch category data:\", error);\r\n  }\r\n\r\n  const isSearch = key?.trim() !== \"\";\r\n\r\n  const metaArray = {\r\n    title: \"TradeReply Categories | Explore Trading Content\",\r\n    description: \"Explore curated content on TradeReply.com. Browse blog articles and educational resources grouped by category to deepen your trading knowledge.\",\r\n    og_title: \"TradeReply Categories | Explore Trading Content\",\r\n    og_description: \"Explore curated content on TradeReply.com. Browse blog articles and educational resources grouped by category to deepen your trading knowledge.\",\r\n    og_site_name: \"TradeReply\",\r\n    twitter_title: \"TradeReply Categories | Explore Trading Content\",\r\n    twitter_description: \"Explore curated content on TradeReply.com. Browse blog articles and educational resources grouped by category to deepen your trading knowledge.\",\r\n    noindex: isSearch,\r\n    ...(isSearch ? {} : { canonical_link: canonicalLink }),\r\n    rel_next: nextLink,\r\n  };\r\n\r\n  // Generate JSON-LD schemas only when NOT in search mode\r\n  let categorySchemas = [];\r\n  if (!isSearch && data.articles && data.articles.length > 0) {\r\n    // Generate CollectionPage schema\r\n    const categoryName = data.selected_category?.title || \"Latest Articles\";\r\n    const collectionPageSchema = generateCollectionPageSchema({\r\n      name: categoryName,\r\n      description: \"Explore curated trading content and educational resources on TradeReply.com\",\r\n      url: canonicalLink,\r\n      articles: data.articles,\r\n      currentPage: page\r\n    });\r\n\r\n    // Generate BreadcrumbList schema\r\n    const breadcrumbItems = generateCategoryBreadcrumbs(categoryName, page > 1 ? page : null);\r\n    const breadcrumbSchema = generateBreadcrumbListSchema({\r\n      items: breadcrumbItems\r\n    });\r\n\r\n    // Add schemas to array (only if they were generated successfully)\r\n    if (collectionPageSchema) categorySchemas.push(collectionPageSchema);\r\n    if (breadcrumbSchema) categorySchemas.push(breadcrumbSchema);\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {categorySchemas.length > 0 && <JsonLdSchema schemas={categorySchemas} />}\r\n      <CategoryClient\r\n        initialData={data}\r\n        slug={slug}\r\n        keyWord={key}\r\n        currentPage={page}\r\n        metaArray={metaArray}\r\n      />\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEe,eAAe,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE;IACjE,MAAM,OAAO,cAAc,QAAQ;IACnC,MAAM,OAAO,SAAS,QAAQ,OAAO;IACrC,MAAM,cAAc,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,MAAM,YAAY,GAAG,CAAC,sBAAsB,SAAS;IAE3D,MAAM,gBACJ,SAAS,IACL,CAAC,mCAAmC,CAAC,GACrC,CAAC,yCAAyC,EAAE,MAAM;IAExD,IAAI,OAAO;QACT,eAAe,EAAE;QACjB,UAAU,EAAE;QACZ,MAAM,CAAC;QACP,mBAAmB;IACrB;IAEA,IAAI,qBAAqB,CAAC;IAC1B,IAAI,WAAW;IAEf,IAAI;QACF,MAAM,QAAQ,IAAI,gBAAgB;YAAE;YAAM;YAAK;QAAK,GAAG,QAAQ;QAC/D,MAAM,UAAU,6DAAwC;QACxD,MAAM,MAAM,MAAM,MAAM,GAAG,QAAQ,UAAU,EAAE,OAAO,EAAE;YACtD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,IAAI,EAAE,EAAE;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,IAAI,IAAI;QAC/B,OAAO,SAAS,IAAI;QACpB,QAAQ,GAAG,CAAC,0BAA0B,KAAK,SAAS,CAAC,UAAU,MAAM,MAAM;QAE3E,qBAAqB,KAAK,IAAI;QAC9B,IAAI,oBAAoB,eAAe,oBAAoB,OAAO;YAChE,WAAW,CAAC,yCAAyC,EAAE,mBAAmB,YAAY,GAAG,GAAG;QAC9F;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;IAClD;IAEA,MAAM,WAAW,KAAK,WAAW;IAEjC,MAAM,YAAY;QAChB,OAAO;QACP,aAAa;QACb,UAAU;QACV,gBAAgB;QAChB,cAAc;QACd,eAAe;QACf,qBAAqB;QACrB,SAAS;QACT,GAAI,WAAW,CAAC,IAAI;YAAE,gBAAgB;QAAc,CAAC;QACrD,UAAU;IACZ;IAEA,wDAAwD;IACxD,IAAI,kBAAkB,EAAE;IACxB,IAAI,CAAC,YAAY,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;QAC1D,iCAAiC;QACjC,MAAM,eAAe,KAAK,iBAAiB,EAAE,SAAS;QACtD,MAAM,uBAAuB,CAAA,GAAA,oIAAA,CAAA,+BAA4B,AAAD,EAAE;YACxD,MAAM;YACN,aAAa;YACb,KAAK;YACL,UAAU,KAAK,QAAQ;YACvB,aAAa;QACf;QAEA,iCAAiC;QACjC,MAAM,kBAAkB,CAAA,GAAA,oIAAA,CAAA,8BAA2B,AAAD,EAAE,cAAc,OAAO,IAAI,OAAO;QACpF,MAAM,mBAAmB,CAAA,GAAA,oIAAA,CAAA,+BAA4B,AAAD,EAAE;YACpD,OAAO;QACT;QAEA,kEAAkE;QAClE,IAAI,sBAAsB,gBAAgB,IAAI,CAAC;QAC/C,IAAI,kBAAkB,gBAAgB,IAAI,CAAC;IAC7C;IAEA,qBACE;;YACG,gBAAgB,MAAM,GAAG,mBAAK,8OAAC,oIAAA,CAAA,UAAY;gBAAC,SAAS;;;;;;0BACtD,8OAAC,oJAAA,CAAA,UAAc;gBACb,aAAa;gBACb,MAAM;gBACN,SAAS;gBACT,aAAa;gBACb,WAAW;;;;;;;;AAInB", "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}