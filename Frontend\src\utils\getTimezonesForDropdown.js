export function getTimezonesForDropdown() {
    const timezones = Intl.supportedValuesOf('timeZone');
    const now = new Date();

    const list = timezones.map((tz) => {
        try {
            const offsetMinutes = getTimezoneOffsetMinutes(tz);
            const hours = Math.floor(offsetMinutes / 60);
            const minutes = Math.abs(offsetMinutes % 60);
            const sign = offsetMinutes >= 0 ? '+' : '-';
            const utc = `UTC${sign}${String(Math.abs(hours)).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;

            return {
                label: `(${utc}) ${tz.replace(/_/g, ' ')}`,
                value: tz,
            };
        } catch {
            return null;
        }
    }).filter(Boolean);

    return list.sort((a, b) => a.label.localeCompare(b.label));
}

function getTimezoneOffsetMinutes(timeZone) {
    const now = new Date();
    const localeString = now.toLocaleString('en-US', { timeZone });
    const converted = new Date(localeString);
    return Math.round((converted - now) / 60000);
}
