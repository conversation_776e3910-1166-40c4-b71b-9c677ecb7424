{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/education/%5Bdetail%5D/components/EducationContent.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Home)/education/[detail]/components/EducationContent.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Home)/education/[detail]/components/EducationContent.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoU,GACjW,kGACA", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/education/%5Bdetail%5D/components/EducationContent.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Home)/education/[detail]/components/EducationContent.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Home)/education/[detail]/components/EducationContent.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgT,GAC7U,8EACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Layouts/HomeLayout.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Layouts/HomeLayout.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Layouts/HomeLayout.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Layouts/HomeLayout.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Layouts/HomeLayout.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Layouts/HomeLayout.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/education/%5Bdetail%5D/page.js"], "sourcesContent": ["import { unstable_noStore as noStore } from 'next/cache';\r\nimport EducationContent from \"./components/EducationContent\";\r\nimport HomeLayout from \"@/Layouts/HomeLayout\";\r\nimport MetaHead from \"@/Seo/Meta/MetaHead\";\r\nimport { Container } from \"react-bootstrap\";\r\nimport Script from \"next/script\"; // <-- add this import\r\n\r\nasync function fetchEducationDetail(detail) {\r\n  const url = new URL(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/article/education/${detail}`);\r\n\r\n  const res = await fetch(url.toString(), {\r\n    cache: \"no-store\",\r\n  });\r\n\r\n  if (!res.ok) throw new Error(`API error: ${res.status}`);\r\n  return res.json();\r\n}\r\n\r\nexport async function generateMetadata({ params }) {\r\n  noStore();\r\n\r\n  const resolvedParams = await params;\r\n  const detailSlug = resolvedParams.detail;\r\n  const data = await fetchEducationDetail(detailSlug);\r\n  const environment = process.env.NEXT_PUBLIC_ENVIRONMENT;\r\n  return {\r\n    title: `What is ${data.data.title} | TradeReply Education`,\r\n    description: data.data.summary,\r\n    openGraph: {\r\n      title: `What is ${data.data.title} | TradeReply Education`,\r\n      description: data.data.summary,\r\n      images: [{\r\n                url: data?.data?.feature_image_url || 'https://www.tradereply.com/images/tradereply-trading-analytics-og.jpg', // Replace with actual default image if needed\r\n                width: 1200,\r\n                height: 630,\r\n             }],\r\n    },\r\n     twitter: {\r\n         title: `What is ${data?.data?.title} | TradeReply Education`,\r\n         description: data?.data?.summary,\r\n         site: '@JoinTradeReply',\r\n         images: [data?.data?.feature_image_url || 'https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png'], // Replace with actual default image if needed\r\n       },\r\n       icons: {\r\n                icon: [\r\n                  {\r\n                    url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.ico`,\r\n                    type: \"image/x-icon\",\r\n                  },\r\n                  {\r\n                    url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.svg`,\r\n                    type: \"image/svg+xml\",\r\n                  },\r\n                ],\r\n              },\r\n  };\r\n}\r\n\r\nexport default async function EducationDetail({ params }) {\r\n  noStore();\r\n\r\n  const resolvedParams = await params;\r\n  const detailSlug = resolvedParams.detail;\r\n\r\n  const data = await fetchEducationDetail(detailSlug);\r\n  const articleData = data.data;\r\n\r\n\r\n  return (\r\n    <HomeLayout>\r\n      <Container>\r\n        {/* JSON-LD Article Schema Only */}\r\n        <Script id=\"ld-json-article\" type=\"application/ld+json\">\r\n          {JSON.stringify({\r\n            \"@context\": \"https://schema.org\",\r\n            \"@type\": \"Article\",\r\n            \"mainEntityOfPage\": {\r\n              \"@type\": \"WebPage\",\r\n              \"@id\": `https://www.tradereply.com/education/${detailSlug}`\r\n            },\r\n            \"headline\": articleData.title,\r\n            \"description\": articleData.summary,\r\n            \"author\": {\r\n              \"@type\": \"Organization\",\r\n              \"name\": \"TradeReply\"\r\n            },\r\n            \"publisher\": {\r\n              \"@type\": \"Organization\",\r\n              \"name\": \"TradeReply\",\r\n              \"logo\": {\r\n                \"@type\": \"ImageObject\",\r\n                \"url\": \"https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png\"\r\n              }\r\n            },\r\n            \"datePublished\": new Date(articleData.created_at).toISOString(),\r\n            \"dateModified\": new Date(articleData.updated_at || articleData.created_at).toISOString(),\r\n            \"articleSection\": \"Education\",\r\n            \"articleBody\": articleData.articleBody || \"\"\r\n          })}\r\n        </Script>\r\n        <EducationContent\r\n          detailSlug={detailSlug}\r\n          articleData={data.data}\r\n          nextArticle={data.next_article}\r\n          avgProgress={data.avgProgress}\r\n        />\r\n      </Container>\r\n    </HomeLayout>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA,8NAAkC,sBAAsB;;;;;;;;AAExD,eAAe,qBAAqB,MAAM;IACxC,MAAM,MAAM,IAAI,IAAI,6DAAwC,0BAA0B,EAAE,QAAQ;IAEhG,MAAM,MAAM,MAAM,MAAM,IAAI,QAAQ,IAAI;QACtC,OAAO;IACT;IAEA,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,IAAI,MAAM,EAAE;IACvD,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,iBAAiB,EAAE,MAAM,EAAE;IAC/C,CAAA,GAAA,6HAAA,CAAA,mBAAO,AAAD;IAEN,MAAM,iBAAiB,MAAM;IAC7B,MAAM,aAAa,eAAe,MAAM;IACxC,MAAM,OAAO,MAAM,qBAAqB;IACxC,MAAM;IACN,OAAO;QACL,OAAO,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC;QAC1D,aAAa,KAAK,IAAI,CAAC,OAAO;QAC9B,WAAW;YACT,OAAO,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC;YAC1D,aAAa,KAAK,IAAI,CAAC,OAAO;YAC9B,QAAQ;gBAAC;oBACC,KAAK,MAAM,MAAM,qBAAqB;oBACtC,OAAO;oBACP,QAAQ;gBACX;aAAE;QACX;QACC,SAAS;YACL,OAAO,CAAC,QAAQ,EAAE,MAAM,MAAM,MAAM,uBAAuB,CAAC;YAC5D,aAAa,MAAM,MAAM;YACzB,MAAM;YACN,QAAQ;gBAAC,MAAM,MAAM,qBAAqB;aAA0E;QACtH;QACA,OAAO;YACE,MAAM;gBACJ;oBACE,KAAK,CAAC,2BAA2B,EAAE,YAAY,mCAAmC,CAAC;oBACnF,MAAM;gBACR;gBACA;oBACE,KAAK,CAAC,2BAA2B,EAAE,YAAY,mCAAmC,CAAC;oBACnF,MAAM;gBACR;aACD;QACH;IACZ;AACF;AAEe,eAAe,gBAAgB,EAAE,MAAM,EAAE;IACtD,CAAA,GAAA,6HAAA,CAAA,mBAAO,AAAD;IAEN,MAAM,iBAAiB,MAAM;IAC7B,MAAM,aAAa,eAAe,MAAM;IAExC,MAAM,OAAO,MAAM,qBAAqB;IACxC,MAAM,cAAc,KAAK,IAAI;IAG7B,qBACE,8OAAC,4HAAA,CAAA,UAAU;kBACT,cAAA,8OAAC,8LAAA,CAAA,YAAS;;8BAER,8OAAC,8HAAA,CAAA,UAAM;oBAAC,IAAG;oBAAkB,MAAK;8BAC/B,KAAK,SAAS,CAAC;wBACd,YAAY;wBACZ,SAAS;wBACT,oBAAoB;4BAClB,SAAS;4BACT,OAAO,CAAC,qCAAqC,EAAE,YAAY;wBAC7D;wBACA,YAAY,YAAY,KAAK;wBAC7B,eAAe,YAAY,OAAO;wBAClC,UAAU;4BACR,SAAS;4BACT,QAAQ;wBACV;wBACA,aAAa;4BACX,SAAS;4BACT,QAAQ;4BACR,QAAQ;gCACN,SAAS;gCACT,OAAO;4BACT;wBACF;wBACA,iBAAiB,IAAI,KAAK,YAAY,UAAU,EAAE,WAAW;wBAC7D,gBAAgB,IAAI,KAAK,YAAY,UAAU,IAAI,YAAY,UAAU,EAAE,WAAW;wBACtF,kBAAkB;wBAClB,eAAe,YAAY,WAAW,IAAI;oBAC5C;;;;;;8BAEF,8OAAC,mLAAA,CAAA,UAAgB;oBACf,YAAY;oBACZ,aAAa,KAAK,IAAI;oBACtB,aAAa,KAAK,YAAY;oBAC9B,aAAa,KAAK,WAAW;;;;;;;;;;;;;;;;;AAKvC", "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}