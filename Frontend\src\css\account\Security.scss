@use "../theme/var";

.security_sec {
  .account_card_list {
    ul {
      li {
        justify-content: flex-start;

        span {
          padding-right: 15px;

          @media screen and (min-width: 768px) {
            // width: 200px;
          }
        }
      }
    }

    p {
      color: var.$textclr;
    }
  }

  .table {
    &_heading {
      border: 1px solid var.$borderclr;
      border-radius: 15px;
      padding: 10px 1.25rem;
    }
  }
}
.modal-content {
  background-color: #031940 !important;
  border: 1px solid var.$clr04498C;
  border-radius: 15px;
  color: #ffffff;
}
.account_card_list_btns {
  display: flex;
  justify-content: end;
  align-items: center;
  gap: 10px;
}


/* Responsive styles for small screens */
@media (max-width: 360px) {
  .account_card_list_btns {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
  }
}

@media (max-width: 360px) {
  .confirm-modal-btn {
    justify-content: center !important;
    gap: 8px !important;
    flex-wrap: wrap;
  }
  .confirm-modal-btn But<PERSON> {
   
  }

 
}


