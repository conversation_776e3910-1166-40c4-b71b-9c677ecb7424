module.exports = {

"[project]/.next-internal/server/app/(Home)/blog/[detail]/page/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[project]/src/app/layout.js [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/layout.js [app-rsc] (ecmascript)"));
}}),
"[project]/src/app/not-found.js [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/not-found.js [app-rsc] (ecmascript)"));
}}),
"[project]/src/Layouts/HomeLayout.js (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/Layouts/HomeLayout.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/Layouts/HomeLayout.js <module evaluation>", "default");
}}),
"[project]/src/Layouts/HomeLayout.js (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/Layouts/HomeLayout.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/Layouts/HomeLayout.js", "default");
}}),
"[project]/src/Layouts/HomeLayout.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Layouts$2f$HomeLayout$2e$js__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/Layouts/HomeLayout.js (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Layouts$2f$HomeLayout$2e$js__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/Layouts/HomeLayout.js (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Layouts$2f$HomeLayout$2e$js__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/constants/index.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SYSTEM_ROLES": (()=>SYSTEM_ROLES),
    "blogLimit": (()=>blogLimit),
    "placeHolderImg": (()=>placeHolderImg)
});
const placeHolderImg = "/images/tradereply-placeholder.jpg";
const blogLimit = 300;
const SYSTEM_ROLES = {
    ADMIN: "admin",
    USER: "user",
    SUPER_ADMIN: "Super admin"
};
}}),
"[project]/src/Components/common/Home/RecentPost.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-rsc] (ecmascript)"); // Import Next.js Link
;
;
;
;
const RecentPost = ({ img, title, text, coinname = null, className, time = null, href, category = null })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Fragment"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
            href: href,
            className: `recent_post ${className}`,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "recent_post_img",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                        src: img || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["placeHolderImg"],
                        alt: title ? `Recent blog post: ${title}` : "TradeReply blog article thumbnail"
                    }, void 0, false, {
                        fileName: "[project]/src/Components/common/Home/RecentPost.js",
                        lineNumber: 12,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/Components/common/Home/RecentPost.js",
                    lineNumber: 11,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "recent_post_content",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                            children: title
                        }, void 0, false, {
                            fileName: "[project]/src/Components/common/Home/RecentPost.js",
                            lineNumber: 17,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            children: text ?? "N/A"
                        }, void 0, false, {
                            fileName: "[project]/src/Components/common/Home/RecentPost.js",
                            lineNumber: 18,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("small", {
                            children: coinname
                        }, void 0, false, {
                            fileName: "[project]/src/Components/common/Home/RecentPost.js",
                            lineNumber: 19,
                            columnNumber: 11
                        }, this),
                        time && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "recent_post_time",
                            children: time
                        }, void 0, false, {
                            fileName: "[project]/src/Components/common/Home/RecentPost.js",
                            lineNumber: 20,
                            columnNumber: 20
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/Components/common/Home/RecentPost.js",
                    lineNumber: 16,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/Components/common/Home/RecentPost.js",
            lineNumber: 10,
            columnNumber: 7
        }, this)
    }, void 0, false);
};
const __TURBOPACK__default__export__ = RecentPost;
{}{}{}}}),
"[project]/src/Components/UI/CustomBreadcrumb.jsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/Components/UI/CustomBreadcrumb.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/Components/UI/CustomBreadcrumb.jsx <module evaluation>", "default");
}}),
"[project]/src/Components/UI/CustomBreadcrumb.jsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/Components/UI/CustomBreadcrumb.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/Components/UI/CustomBreadcrumb.jsx", "default");
}}),
"[project]/src/Components/UI/CustomBreadcrumb.jsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$CustomBreadcrumb$2e$jsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/Components/UI/CustomBreadcrumb.jsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$CustomBreadcrumb$2e$jsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/Components/UI/CustomBreadcrumb.jsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$CustomBreadcrumb$2e$jsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/app/(Home)/blog/[detail]/components/BlogContent.js (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/(Home)/blog/[detail]/components/BlogContent.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/(Home)/blog/[detail]/components/BlogContent.js <module evaluation>", "default");
}}),
"[project]/src/app/(Home)/blog/[detail]/components/BlogContent.js (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/(Home)/blog/[detail]/components/BlogContent.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/(Home)/blog/[detail]/components/BlogContent.js", "default");
}}),
"[project]/src/app/(Home)/blog/[detail]/components/BlogContent.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$Home$292f$blog$2f5b$detail$5d2f$components$2f$BlogContent$2e$js__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/app/(Home)/blog/[detail]/components/BlogContent.js (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$Home$292f$blog$2f5b$detail$5d2f$components$2f$BlogContent$2e$js__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/app/(Home)/blog/[detail]/components/BlogContent.js (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$Home$292f$blog$2f5b$detail$5d2f$components$2f$BlogContent$2e$js__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/Seo/Schema/JsonLdSchema.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
/**
 * JsonLdSchema Component
 * 
 * Renders JSON-LD structured data schemas for SEO purposes.
 * Each schema is rendered in its own separate <script type="application/ld+json"> tag
 * to ensure proper search engine crawling and indexing.
 * 
 * Usage:
 * <JsonLdSchema schemas={[organizationSchema, websiteSchema]} />
 */ __turbopack_context__.s({
    "cleanKeywords": (()=>cleanKeywords),
    "default": (()=>JsonLdSchema),
    "formatDateToISO": (()=>formatDateToISO),
    "generateBlogPostingSchema": (()=>generateBlogPostingSchema),
    "generateBreadcrumbListSchema": (()=>generateBreadcrumbListSchema),
    "generateCategoryBreadcrumbs": (()=>generateCategoryBreadcrumbs),
    "generateCollectionPageSchema": (()=>generateCollectionPageSchema),
    "generateOrganizationSchema": (()=>generateOrganizationSchema),
    "generateProductSchema": (()=>generateProductSchema),
    "generateWebsiteSchema": (()=>generateWebsiteSchema),
    "getBlogSlug": (()=>getBlogSlug),
    "selectReviewsForSchema": (()=>selectReviewsForSchema),
    "shuffleArray": (()=>shuffleArray)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
;
function JsonLdSchema({ schemas = [] }) {
    if (!schemas || schemas.length === 0) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Fragment"], {
        children: schemas.map((schema, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("script", {
                type: "application/ld+json",
                dangerouslySetInnerHTML: {
                    __html: JSON.stringify(schema, null, 0)
                }
            }, index, false, {
                fileName: "[project]/src/Seo/Schema/JsonLdSchema.js",
                lineNumber: 20,
                columnNumber: 9
            }, this))
    }, void 0, false);
}
const generateOrganizationSchema = ()=>{
    return {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "TradeReply",
        "url": "https://www.tradereply.com",
        "logo": "https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png",
        "contactPoint": {
            "@type": "ContactPoint",
            "url": "https://www.tradereply.com/help",
            "contactType": "Customer Support",
            "areaServed": "Global",
            "availableLanguage": "English"
        },
        "sameAs": [
            "https://www.facebook.com/TradeReply",
            "https://www.instagram.com/tradereply",
            "https://x.com/JoinTradeReply"
        ]
    };
};
const generateWebsiteSchema = ()=>{
    return {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "url": "https://www.tradereply.com/",
        "name": "TradeReply"
    };
};
const generateBlogPostingSchema = ({ canonicalUrl, headline, description, imageUrl, datePublished, dateModified, articleBody, keywords })=>{
    // Only generate schema if required fields are present
    if (!canonicalUrl || !headline) {
        return null;
    }
    return {
        "@context": "https://schema.org",
        "@type": "BlogPosting",
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": canonicalUrl
        },
        "headline": headline,
        "description": description || "",
        "image": imageUrl || "",
        "author": {
            "@type": "Organization",
            "name": "TradeReply"
        },
        "publisher": {
            "@type": "Organization",
            "name": "TradeReply",
            "logo": {
                "@type": "ImageObject",
                "url": "https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png"
            }
        },
        "datePublished": datePublished || "",
        "dateModified": dateModified || datePublished || "",
        "articleBody": articleBody || description || "",
        "keywords": keywords || ""
    };
};
const formatDateToISO = (date)=>{
    if (!date) return null;
    try {
        // Handle different date formats
        let dateObj;
        if (typeof date === 'string') {
            dateObj = new Date(date);
        } else if (date instanceof Date) {
            dateObj = date;
        } else {
            return null;
        }
        // Check if date is valid
        if (isNaN(dateObj.getTime())) {
            return null;
        }
        return dateObj.toISOString();
    } catch (error) {
        console.warn('Error formatting date to ISO:', error);
        return null;
    }
};
const getBlogSlug = (blog)=>{
    if (!blog) return '';
    // If slug exists, use it directly
    if (blog.slug) {
        return blog.slug;
    }
    // Fallback: generate slug from title
    if (blog.title) {
        return blog.title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');
    }
    return '';
};
const cleanKeywords = (keywords)=>{
    if (!keywords || typeof keywords !== 'string') {
        return '';
    }
    return keywords.split(',').map((keyword)=>keyword.trim()).filter((keyword)=>keyword.length > 0).join(', ');
};
const generateProductSchema = ({ name, description, image, brand, price, currency = "USD", availability = "http://schema.org/InStock", url, seller, aggregateRating, reviews = [] })=>{
    // Only generate schema if required fields are present
    if (!name || !price) {
        return null;
    }
    const schema = {
        "@context": "https://schema.org",
        "@type": "Product",
        "name": name,
        "description": description || "",
        "image": image || "",
        "offers": {
            "@type": "Offer",
            "price": price.toString(),
            "priceCurrency": currency,
            "availability": availability,
            "url": url || ""
        }
    };
    // Add brand if provided
    if (brand) {
        schema.brand = {
            "@type": "Brand",
            "name": brand
        };
    }
    // Add seller if provided
    if (seller) {
        schema.offers.seller = {
            "@type": "Organization",
            "name": seller.name || "",
            "url": seller.url || ""
        };
    }
    // Add aggregate rating if provided
    if (aggregateRating && aggregateRating.ratingValue && aggregateRating.reviewCount) {
        schema.aggregateRating = {
            "@type": "AggregateRating",
            "ratingValue": aggregateRating.ratingValue.toString(),
            "reviewCount": aggregateRating.reviewCount.toString()
        };
    }
    // Add reviews if provided (maximum 3)
    if (reviews && reviews.length > 0) {
        schema.review = reviews.slice(0, 3).map((review)=>({
                "@type": "Review",
                "author": {
                    "@type": "Person",
                    "name": review.author || "Anonymous"
                },
                "datePublished": formatDateToISO(review.datePublished) || "",
                "reviewBody": review.reviewBody || "",
                "reviewRating": {
                    "@type": "Rating",
                    "ratingValue": review.rating ? review.rating.toString() : "5"
                }
            }));
    }
    return schema;
};
const generateCollectionPageSchema = ({ name, description, url, articles = [], currentPage = 1 })=>{
    // Only generate schema if required fields are present
    if (!name || !url) {
        return null;
    }
    const pageTitle = currentPage > 1 ? `${name} – Page ${currentPage}` : name;
    const schema = {
        "@context": "https://schema.org",
        "@type": "CollectionPage",
        "name": pageTitle,
        "description": description || "",
        "url": url
    };
    // Add articles as ListItem elements (maximum 10)
    if (articles && articles.length > 0) {
        schema.mainEntity = {
            "@type": "ItemList",
            "numberOfItems": articles.length,
            "itemListElement": articles.slice(0, 10).map((article, index)=>({
                    "@type": "ListItem",
                    "position": index + 1,
                    "item": {
                        "@type": article.type === 'blog' ? "BlogPosting" : "Article",
                        "@id": `https://www.tradereply.com/${article.type}/${article.slug}`,
                        "name": article.title || "",
                        "description": article.summary || "",
                        "datePublished": formatDateToISO(article.created_at) || "",
                        "author": {
                            "@type": "Organization",
                            "name": "TradeReply"
                        }
                    }
                }))
        };
    }
    return schema;
};
const generateBreadcrumbListSchema = ({ items = [] })=>{
    // Only generate schema if items are provided
    if (!items || items.length === 0) {
        return null;
    }
    return {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": items.map((item, index)=>({
                "@type": "ListItem",
                "position": index + 1,
                "name": item.name,
                "item": item.url
            }))
    };
};
const selectReviewsForSchema = (allReviews = [], averageRating = 5, maxReviews = 3)=>{
    if (!allReviews || allReviews.length === 0) {
        return [];
    }
    // Round average rating to nearest integer for selection logic
    const targetRating = Math.round(averageRating);
    // Filter reviews by target rating
    const targetReviews = allReviews.filter((review)=>Math.round(parseFloat(review.rating || 5)) === targetRating);
    // If we have enough reviews of the target rating, use them
    if (targetReviews.length >= maxReviews) {
        return shuffleArray(targetReviews).slice(0, maxReviews);
    }
    // If not enough target reviews, include nearby ratings
    const nearbyRatings = [
        targetRating,
        targetRating - 1,
        targetRating + 1
    ].filter((r)=>r >= 1 && r <= 5);
    const nearbyReviews = allReviews.filter((review)=>nearbyRatings.includes(Math.round(parseFloat(review.rating || 5))));
    return shuffleArray(nearbyReviews).slice(0, maxReviews);
};
const shuffleArray = (array)=>{
    const shuffled = [
        ...array
    ];
    for(let i = shuffled.length - 1; i > 0; i--){
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [
            shuffled[j],
            shuffled[i]
        ];
    }
    return shuffled;
};
const generateCategoryBreadcrumbs = (categoryName = "All Articles", currentPage = null)=>{
    const breadcrumbs = [
        {
            name: "Home",
            url: "https://www.tradereply.com/"
        },
        {
            name: categoryName,
            url: "https://www.tradereply.com/category"
        }
    ];
    // Add page breadcrumb for paginated pages
    if (currentPage && currentPage > 1) {
        breadcrumbs.push({
            name: `Page ${currentPage}`,
            url: `https://www.tradereply.com/category/page/${currentPage}`
        });
    }
    return breadcrumbs;
};
}}),
"[project]/src/app/(Home)/blog/[detail]/page.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>BlogDetail),
    "generateMetadata": (()=>generateMetadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/cache.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Layouts$2f$HomeLayout$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/Layouts/HomeLayout.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$common$2f$Home$2f$RecentPost$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/Components/common/Home/RecentPost.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Components$2f$UI$2f$CustomBreadcrumb$2e$jsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/Components/UI/CustomBreadcrumb.jsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dayjs/dayjs.min.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$plugin$2f$relativeTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dayjs/plugin/relativeTime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$Home$292f$blog$2f5b$detail$5d2f$components$2f$BlogContent$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/(Home)/blog/[detail]/components/BlogContent.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dompurify$2f$dist$2f$purify$2e$es$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dompurify/dist/purify.es.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Seo$2f$Schema$2f$JsonLdSchema$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/Seo/Schema/JsonLdSchema.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].extend(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$plugin$2f$relativeTime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"]);
;
;
;
async function fetchBlog(detail) {
    const res = await fetch(`${("TURBOPACK compile-time value", "http://127.0.0.1:8000")}/api/v1/article/blog/${detail}`);
    if (!res.ok) {
        throw new Error(`API error: ${res.status}`);
    }
    return res.json();
}
async function BlogDetail({ params }) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unstable_noStore"])();
    const resolvedParams = await params;
    const detail = resolvedParams.detail;
    const response = await fetchBlog(detail);
    const blog = response?.data;
    // Generate JSON-LD schema for blog article if blog data exists
    let blogSchema = null;
    if (blog) {
        const blogSlug = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Seo$2f$Schema$2f$JsonLdSchema$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBlogSlug"])(blog);
        const canonicalUrl = `https://www.tradereply.com/blog/${blogSlug}`;
        blogSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Seo$2f$Schema$2f$JsonLdSchema$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateBlogPostingSchema"])({
            canonicalUrl,
            headline: blog.title,
            description: blog.summary,
            imageUrl: blog.feature_image_url,
            datePublished: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Seo$2f$Schema$2f$JsonLdSchema$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatDateToISO"])(blog.created_at),
            dateModified: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Seo$2f$Schema$2f$JsonLdSchema$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatDateToISO"])(blog.updated_at),
            // Note: These fields require backend implementation
            articleBody: blog.schema_article_body || blog.summary,
            keywords: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Seo$2f$Schema$2f$JsonLdSchema$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cleanKeywords"])(blog.schema_keywords) || ""
        });
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            blogSchema && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Seo$2f$Schema$2f$JsonLdSchema$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                schemas: [
                    blogSchema
                ]
            }, void 0, false, {
                fileName: "[project]/src/app/(Home)/blog/[detail]/page.js",
                lineNumber: 55,
                columnNumber: 24
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$Home$292f$blog$2f5b$detail$5d2f$components$2f$BlogContent$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                response: response
            }, void 0, false, {
                fileName: "[project]/src/app/(Home)/blog/[detail]/page.js",
                lineNumber: 56,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true);
}
async function generateMetadata({ params }) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unstable_noStore"])();
    const resolvedParams = await params;
    const detail = resolvedParams.detail;
    const response = await fetchBlog(detail);
    const blog = response.data;
    const environment = ("TURBOPACK compile-time value", "dev");
    return {
        title: blog.title ? `${blog.title} | TradeReply Blog` : 'TradeReply Blog | Insights & Strategies for Traders',
        description: blog.summary || 'Explore insights on TradeReply.',
        robots: "index, follow",
        openGraph: {
            title: `${blog.title} | TradeReply Blog`,
            description: blog?.summary,
            siteName: 'TradeReply',
            type: 'article',
            images: [
                {
                    url: blog?.feature_image_url || 'https://www.tradereply.com/images/tradereply-trading-analytics-og.jpg',
                    width: 1200,
                    height: 630
                }
            ],
            locale: 'en_US'
        },
        twitter: {
            title: `${blog?.title} | TradeReply Blog`,
            description: blog?.summary,
            site: '@JoinTradeReply',
            images: [
                blog?.feature_image_url || 'https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png'
            ]
        },
        icons: {
            icon: [
                {
                    url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.ico`,
                    type: "image/x-icon"
                },
                {
                    url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.svg`,
                    type: "image/svg+xml"
                }
            ]
        }
    };
}
}}),
"[project]/src/app/(Home)/blog/[detail]/page.js [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/(Home)/blog/[detail]/page.js [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=_7e7817a7._.js.map