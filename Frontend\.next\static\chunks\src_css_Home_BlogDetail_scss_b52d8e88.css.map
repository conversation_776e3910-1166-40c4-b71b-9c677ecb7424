{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/css/Home/BlogDetail.scss.css"], "sourcesContent": [":root{--font-g<PERSON>roy: \"<PERSON><PERSON>\", sans-serif}.blog_detail_tag{padding:6px 20px;background-color:#00adef;border-radius:10px;text-align:center;font-size:16px;font-weight:600;line-height:26px;letter-spacing:-.1000000015px;text-transform:uppercase;color:#fff;border:0}.blog_detail_heading h1{font-size:2.8rem;font-weight:600;color:#fff;padding:30px 0}@media(max-width: 1199px){.blog_detail_heading h1{font-size:1.5rem}}@media(max-width: 767px){.blog_detail_heading h1{font-size:1.5rem;line-height:35px}}.blog_detail_heading h5{font-size:1.25rem;font-weight:600;color:#fff;padding-top:30px}@media(max-width: 767px){.blog_detail_heading h5{font-size:1.125rem;line-height:30px;padding-top:5px}}.blog_detail_postimg{padding:5rem 0}@media(max-width: 767px){.blog_detail_postimg{padding:2rem 0}}.blog_detail_postimg img{border-radius:60px;width:100%}@media(max-width: 767px){.blog_detail_postimg img{border-radius:30px}}.blog_detail_text p{font-size:1.5rem;font-weight:400;line-height:36px;letter-spacing:-0.1px;color:#fff;padding-top:20px;max-width:1000px;white-space:normal;word-wrap:break-word;overflow:visible;text-overflow:clip}@media(max-width: 767px){.blog_detail_text p{font-size:1rem;line-height:24px;padding-top:0}}.blog_detail_author{padding-top:5rem}@media(max-width: 767px){.blog_detail_author{padding-top:3rem}}.blog_detail_author_btn{background-color:rgba(0,0,0,0);border:0;color:#00adef;font-size:1.25rem;font-weight:600;line-height:24.5px;letter-spacing:-.1000000015px;margin-bottom:60px}@media(max-width: 767px){.blog_detail_author_btn{font-size:1rem;line-height:1.25rem;margin-bottom:30px}}.blog_detail .recent_post{background-color:rgba(0,0,0,0);border-radius:0;border:0;margin-bottom:0;padding:30px 0;border-top:1px solid #666;border-bottom:1px solid #666}"], "names": [], "mappings": "AAAA;;;;AAA0C;;;;;;;;;;;;;;AAA2N;;;;;;;AAAmF;EAA0B;;;;;AAA0C;EAAyB;;;;;;AAA2D;;;;;;;AAAsF;EAAyB;;;;;;;AAA6E;;;;AAAoC;EAAyB;;;;;AAAqC;;;;;AAAuD;EAAyB;;;;;AAA6C;;;;;;;;;;;;;;AAAqN;EAAyB;;;;;;;AAAmE;;;;AAAqC;EAAyB;;;;;AAAsC;;;;;;;;;;;AAAoL;EAAyB;;;;;;;AAA+E"}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}