import { Suspense } from "react";
import SetupPhoneNumberComponent from "./SetupPhoneNumberComponent";
import MetaHead from "@/Seo/Meta/MetaHead";

export default function SetupPhoneNumber() {
    const metaArray = {
        title: "Account Phone | Add Number | TradeReply",
        robots: "noindex, nofollow",
        description:
            "Secure your TradeReply account by setting up your verified phone number. Improve account recovery options and receive important notifications instantly.",
        canonical_link: "https://www.tradereply.com/account/phone/setup",
        og_site_name: "TradeReply",
        og_title: "Account Phone | Add Number | TradeReply",
        og_description:
            "Secure your TradeReply account by setting up your verified phone number. Improve account recovery options and receive important notifications instantly.",
        twitter_title: "Account Phone | Add Number | TradeReply",
        twitter_description:
            "Secure your TradeReply account by setting up your verified phone number. Improve account recovery options and receive important notifications instantly.",
    };
    return (
        <Suspense fallback={<div>Loading...</div>}>
          <MetaHead props={metaArray} />
            <SetupPhoneNumberComponent />
        </Suspense>
    );
}
