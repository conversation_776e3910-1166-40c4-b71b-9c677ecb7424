import axiosInstance from './axiosInstance';
import axios from 'axios';

// GET Request Utility
export const get = async (url, params = {}) => {
  try {
    const response = await axiosInstance.get(url, { params });
    return response.data;
  } catch (error) {
    if (error.name === 'AbortError' || axios.isCancel(error)) { // Handle both AbortError and Axios cancel
      console.log('Request aborted:', url); // Optional quiet log
      return null; // Or throw if needed
    }
    console.error('Error with GET request:', error);
    throw error;
  }
};

export const getBrokers = async (url, params = {}) => {
  try {
    const response = await axiosInstance.get(url, { params });
    return response.data;
  } catch (error) {
    console.error('Error with GET request:', error);
    throw error; // Re-throw for further handling
  }
};

// POST Request Utility
export const post = async (url, data) => {
  try {
    const response = await axiosInstance.post(url, data);
    return response.data;
  } catch (error) {
    console.error('Error with POST request:', error);
    throw error;
  }
};

// PUT Request Utility
export const put = async (url, data) => {
  try {
    const response = await axiosInstance.put(url, data);
    return response.data;
  } catch (error) {
    console.error('Error with PUT request:', error);
    throw error;
  }
};

// DELETE Request Utility
export const deleteRequest = async (url) => {
  try {
    const response = await axiosInstance.delete(url);
    return response.data;
  } catch (error) {
    console.error('Error with DELETE request:', error);
    throw error;
  }
};

// Username management utilities
export const updateUsername = async (username) => {
  try {
    const response = await axiosInstance.post('/username/update', { username });
    return response.data;
  } catch (error) {
    console.error('Error updating username:', error);
    throw error;
  }
};

export const checkUsernameAvailability = async (username) => {
  try {
    const response = await axiosInstance.post('/username/check-availability', { username });
    return response.data;
  } catch (error) {
    console.error('Error checking username availability:', error);
    throw error;
  }
};

// Two-Factor Authentication utilities
export const get2FAStatus = async () => {
  try {
    const response = await axiosInstance.get('/2fa/status');
    return response.data;
  } catch (error) {
    console.error('Error fetching 2FA status:', error);
    throw error;
  }
};

export const enable2FA = async () => {
  try {
    const response = await axiosInstance.post('/2fa/enable');
    return response.data;
  } catch (error) {
    console.error('Error enabling 2FA:', error);
    throw error;
  }
};

export const disable2FA = async () => {
  try {
    const response = await axiosInstance.post('/2fa/disable');
    return response.data;
  } catch (error) {
    console.error('Error disabling 2FA:', error);
    throw error;
  }
};

export const generateRestoreCode = async () => {
  try {
    const response = await axiosInstance.post('/2fa/generate-restore-code');
    return response.data;
  } catch (error) {
    console.error('Error generating restore code:', error);
    throw error;
  }
};

export const update2FAAlwaysRequired = async (alwaysRequired) => {
  try {
    const response = await axiosInstance.post('/2fa/update-always-required', {
      always_required: alwaysRequired
    });
    return response.data;
  } catch (error) {
    console.error('Error updating 2FA always required setting:', error);
    throw error;
  }
};

// Secret Questions utilities
export const getSecretQuestions = async () => {
  try {
    const response = await axiosInstance.get('/account/secret-questions');
    return response.data;
  } catch (error) {
    console.error('Error fetching secret questions:', error);
    throw error;
  }
};

export const saveSecretQuestions = async (questions) => {
  try {
    const response = await axiosInstance.post('/account/secret-questions', { questions });
    return response.data;
  } catch (error) {
    console.error('Error saving secret questions:', error);
    throw error;
  }
};

export const updateSecretQuestions = async (questions) => {
  try {
    const response = await axiosInstance.put('/account/secret-questions', { questions });
    return response.data;
  } catch (error) {
    console.error('Error updating secret questions:', error);
    throw error;
  }
};