/* [project]/src/css/Home/BlogDetail.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

.blog_detail_tag {
  padding: 6px 20px;
  background-color: #00adef;
  border-radius: 10px;
  text-align: center;
  font-size: 16px;
  font-weight: 600;
  line-height: 26px;
  letter-spacing: -.1px;
  text-transform: uppercase;
  color: #fff;
  border: 0;
}

.blog_detail_heading h1 {
  font-size: 2.8rem;
  font-weight: 600;
  color: #fff;
  padding: 30px 0;
}

@media (width <= 1199px) {
  .blog_detail_heading h1 {
    font-size: 1.5rem;
  }
}

@media (width <= 767px) {
  .blog_detail_heading h1 {
    font-size: 1.5rem;
    line-height: 35px;
  }
}

.blog_detail_heading h5 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #fff;
  padding-top: 30px;
}

@media (width <= 767px) {
  .blog_detail_heading h5 {
    font-size: 1.125rem;
    line-height: 30px;
    padding-top: 5px;
  }
}

.blog_detail_postimg {
  padding: 5rem 0;
}

@media (width <= 767px) {
  .blog_detail_postimg {
    padding: 2rem 0;
  }
}

.blog_detail_postimg img {
  border-radius: 60px;
  width: 100%;
}

@media (width <= 767px) {
  .blog_detail_postimg img {
    border-radius: 30px;
  }
}

.blog_detail_text p {
  font-size: 1.5rem;
  font-weight: 400;
  line-height: 36px;
  letter-spacing: -.1px;
  color: #fff;
  padding-top: 20px;
  max-width: 1000px;
  white-space: normal;
  word-wrap: break-word;
  overflow: visible;
  text-overflow: clip;
}

@media (width <= 767px) {
  .blog_detail_text p {
    font-size: 1rem;
    line-height: 24px;
    padding-top: 0;
  }
}

.blog_detail_author {
  padding-top: 5rem;
}

@media (width <= 767px) {
  .blog_detail_author {
    padding-top: 3rem;
  }
}

.blog_detail_author_btn {
  background-color: #0000;
  border: 0;
  color: #00adef;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 24.5px;
  letter-spacing: -.1px;
  margin-bottom: 60px;
}

@media (width <= 767px) {
  .blog_detail_author_btn {
    font-size: 1rem;
    line-height: 1.25rem;
    margin-bottom: 30px;
  }
}

.blog_detail .recent_post {
  background-color: #0000;
  border-radius: 0;
  border: 0;
  margin-bottom: 0;
  padding: 30px 0;
  border-top: 1px solid #666;
  border-bottom: 1px solid #666;
}

/*# sourceMappingURL=src_css_Home_BlogDetail_scss_b52d8e88.css.map*/