/* [project]/src/css/common/CommonTable.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

.tableless {
  border: 1px solid #00adef;
  border-radius: 2rem;
  position: relative;
  z-index: 1;
}

.tableless:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("https://cdn.tradereply.com/dev/site-assets/tradereply-crypto-stock-analysis.png");
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100%;
  border-radius: 2rem;
  z-index: -1;
}

.tableless .common_table tr th, .tableless .common_table tr td {
  padding: 1.6rem 1rem;
  white-space: nowrap;
  vertical-align: middle;
  border: 1px solid #34415b;
}

@media (width <= 767px) {
  .tableless .common_table tr th, .tableless .common_table tr td {
    padding: 1.5rem .5rem;
  }
}

.tableless .common_table tr th .clickIcon, .tableless .common_table tr td .clickIcon {
  cursor: pointer;
}

.tableless .common_table tr td {
  font-weight: 500 !important;
}

.tableless .common_table thead {
  border: none;
}

.tableless .common_table thead tr {
  position: sticky;
  top: 0;
}

.tableless .common_table thead tr th {
  background-color: #000;
  font-size: 1.25rem;
  font-weight: 600;
  color: #fff;
  border-radius: 0;
  border-top: 0;
}

@media (width <= 1199px) {
  .tableless .common_table thead tr th {
    font-size: 1rem;
  }
}

@media (width <= 767px) {
  .tableless .common_table thead tr th {
    font-size: .875rem;
  }
}

.tableless .common_table thead tr th:first-child {
  border-top-left-radius: 2rem;
  border-left: 0;
}

.tableless .common_table thead tr th:last-child {
  border-top-right-radius: 2rem;
  border-right: 0;
}

.tableless .common_table tbody tr:last-child {
  border-bottom: 0;
}

.tableless .common_table tbody tr:last-child td {
  border-bottom: 0;
}

.tableless .common_table tbody td {
  background: none;
  font-size: 1.125rem;
  font-weight: 600;
  color: #e9e9e9;
}

@media (width <= 1199px) {
  .tableless .common_table tbody td {
    font-size: 1rem;
  }
}

@media (width <= 767px) {
  .tableless .common_table tbody td {
    font-size: .875rem;
  }
}

.tableless .common_table tbody td a {
  color: #00adef;
}

.tableless .common_table tbody td:first-child {
  border-left: 0;
}

.tableless .common_table tbody td:last-child {
  border-right: 0;
}

.tableless .common_table tbody tr.no_record td {
  padding: 1rem 0;
}

.no_record_box {
  padding: 3.125rem 1rem;
  text-align: center;
}

.no_record_box svg {
  opacity: 1;
}

.no_record_box svg path {
  fill: #fff;
}

.no_record_box img {
  width: 150px !important;
}

.no_record_box h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-top: .625rem;
  color: #fff;
}

@media (width <= 1679px) {
  .no_record_box h4 {
    font-size: .875rem;
  }
}

@media (width <= 991px) {
  .no_record_box {
    padding: 2.5rem 1.25rem;
  }
}

/*# sourceMappingURL=src_css_common_CommonTable_scss_b52d8e88.css.map*/