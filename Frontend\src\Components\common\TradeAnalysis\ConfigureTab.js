import { Col, Row } from "react-bootstrap";
import { useState, React } from "react";
import "@/css/dashboard/TradeAnalysis.scss";
import FilterTradeDetails from "@/Components/common/TradeAnalysis/FilterTradeDetails";
import DataTradeDetails from "@/Components/common/TradeAnalysis/DataTradeDetails";
import LayoutTradeDetails from "@/Components/common/TradeAnalysis/LayoutTradeDetails";
import {
  DndContext,
  useSensor,
  useSensors,
  PointerSensor,
} from "@dnd-kit/core";
import { arrayMove } from "@dnd-kit/sortable";
export default function ConfigureTab() {
  const sensors = useSensors(useSensor(PointerSensor));
  const [dimensions, setDimensions] = useState([
    { id: "dim-1", title: "Max Risk Percentage" },
    { id: "dim-2", title: "Stock Unit of Measurement" },
    { id: "dim-3", title: "Region" },
    { id: "dim-4", title: "Product" },
  ]);
  const [rows, setRows] = useState([]);
  const [columns, setColumns] = useState([]);
  const [metrics, setMetrics] = useState([
    { id: "met-1", title: "Max Risk Percentage" },
    { id: "met-2", title: "Stock Unit of Measurement" },
    { id: "met-3", title: "DIMENSION ACCOUNT GROWTH GOAL" },
    { id: "met-4", title: "DIMENSION ACCOUNT STOP RISK VALUE" },
  ]);
  const [values, setValues] = useState([]);
  const [activeId, setActiveId] = useState(null);
  const [overId, setOverId] = useState(null);

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (event.doubleClick) return;
    if (!over) return;

    const from = active.data.current?.from;

    const to = over.data.current?.from || over.id;

    if (active.id === over.id) return;

    const activeItem = active.data.current?.item;

    if (from === to) {
      const oldIndex =
        from === "dimensions"
          ? dimensions.findIndex((item) => item.id === active.id)
          : from === "rows"
            ? rows.findIndex((item) => item.id === active.id)
            : from === "columns"
              ? columns.findIndex((item) => item.id === active.id)
              : from === "metrics"
                ? metrics.findIndex((item) => item.id === active.id)
                : values.findIndex((item) => item.id === active.id);

      const newIndex =
        from === "dimensions"
          ? dimensions.findIndex((item) => item.id === over.id)
          : from === "rows"
            ? rows.findIndex((item) => item.id === over.id)
            : from === "columns"
              ? columns.findIndex((item) => item.id === over.id)
              : from === "metrics"
                ? metrics.findIndex((item) => item.id === over.id)
                : values.findIndex((item) => item.id === over.id);

      if (from === "dimensions") {
        setDimensions(arrayMove(dimensions, oldIndex, newIndex));
      } else if (from === "rows") {
        setRows(arrayMove(rows, oldIndex, newIndex));
      } else if (from === "columns") {
        setColumns(arrayMove(columns, oldIndex, newIndex));
      } else if (from === "metrics") {
        setMetrics(arrayMove(metrics, oldIndex, newIndex));
      } else if (from === "values") {
        setValues(arrayMove(values, oldIndex, newIndex));
      }
      return;
    }

    if (from === "dimensions" && (to === "rows" || to === "columns")) {
      const targetList = to === "rows" ? rows : columns;
      const setTarget = to === "rows" ? setRows : setColumns;

      const alreadyExists =
        rows.some((item) => item.originalId === activeItem.id) ||
        columns.some((item) => item.originalId === activeItem.id);

      if (alreadyExists) {
        return;
      }

      const copiedItem = {
        ...activeItem,
        id: `${activeItem.id}-${to}-${Date.now()}`,
        originalId: activeItem.id,
      };

      const overIndex = targetList.findIndex((item) => item.id === over.id);

      if (overIndex === -1) {
        setTarget([...targetList, copiedItem]);
      } else {
        const updated = [...targetList];
        updated.splice(overIndex, 0, copiedItem);
        setTarget(updated);
      }
    } else if (from === "metrics" && to === "values") {
      if (values.some((item) => item.originalId === activeItem.id)) return;

      const copiedItem = {
        ...activeItem,
        id: `${activeItem.id}-values-${Date.now()}`,
        originalId: activeItem.id,
      };

      const overIndex = values.findIndex((item) => item.id === over.id);

      if (overIndex === -1) {
        setValues([...values, copiedItem]);
      } else {
        const updated = [...values];
        updated.splice(overIndex, 0, copiedItem);
        setValues(updated);
      }
    }
  };

  return (
    <>
      <div className="trade_analysis_card">
        <DndContext
          sensors={sensors}
          onDragStart={({ active }) => setActiveId(active.id)}
          onDragOver={({ over }) => setOverId(over?.id)}
          onDragEnd={(event) => {
            handleDragEnd(event);
            setActiveId(null);
            setOverId(null);
          }}
        >
          <Row>
            <Col lg={4} xs={12}>
              <FilterTradeDetails />
            </Col>
            <Col lg={4} xs={12}>
              <DataTradeDetails
                metrics={metrics}
                values={values}
                rows={rows}
                columns={columns}
                valueSet={(dim) => setValues(dim)}
                rowSet={(row) => setRows(row)}
                setDimensionRow={(row) => setDimensions(row)}
                setMetricRow={(row) => setMetrics(row)}
                dimensions={dimensions}
              />
            </Col>
            <Col lg={4} xs={12}>
              <LayoutTradeDetails
                rowss={rows}
                values={values}
                deleteValue={(row) => setValues(row)}
                deleteRow={(row) => setRows(row)}
                deleteColumn={(col) => setColumns(col)}
                columns={columns}
                overId={overId}
                activeId={activeId}
              />
            </Col>
          </Row>
        </DndContext>
      </div>
    </>
  );
}
