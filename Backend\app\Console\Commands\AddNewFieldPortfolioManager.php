<?php

namespace App\Console\Commands;

use App\Models\Article;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\PortfolioFieldDefinition;
use App\Models\FieldDefinition;

class AddNewFieldPortfolioManager extends Command
{
    protected $signature = 'create:portfolio-manager-value';
    protected $description = 'Create Portfolio manager value';

    public function handle()
    {
          $fieldDefinition = FieldDefinition::create([
             'field_name'      => 'NUMBER FORMAT DISPLAY',
             'database_field'  => 'number_format_display',
             'summary'         => 'Choose how numbers, such as prices and metrics, are displayed throughout your dashboard. This only affects visual formatting—not calculations or how you enter numbers.
                                   • English format: 1,234.56
                                   • European format: 1.234,56
                                   Note: When entering values into input fields, always use the international format with a period (.) as the decimal separator. Commas are not allowed in number inputs.',
             'datatype'        => 'Text',
             'expected_values' => 'en-US, de-DE',
             'has_formula'     => false,
             'metric_dimension'=> 'metric'
        ]);

        PortfolioFieldDefinition::create([
             'field_definition_id'  => $fieldDefinition->id,
             'database_field'      => 'number_format_display',
             'summary'             => 'Choose how numbers, such as prices and metrics, are displayed throughout your dashboard. This only affects visual formatting—not calculations or how you enter numbers.
                                                                         • English format: 1,234.56
                                                                         • European format: 1.234,56
                                                                         Note: When entering values into input fields, always use the international format with a period (.) as the decimal separator. Commas are not allowed in number inputs.',
             'account_field'       => 'YES',
             'account_field_placeholder' => 'Select',
             'account_field_value' => 'en-US'
        ]);
    }
}
