{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Layouts/HomeLayout.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Layouts/HomeLayout.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Layouts/HomeLayout.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/Layouts/HomeLayout.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Layouts/HomeLayout.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Layouts/HomeLayout.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/category/%5Bdetail%5D/components/CategoryContent.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Home)/category/[detail]/components/CategoryContent.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Home)/category/[detail]/components/CategoryContent.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkU,GAC/V,gGACA", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/category/%5Bdetail%5D/components/CategoryContent.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Home)/category/[detail]/components/CategoryContent.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Home)/category/[detail]/components/CategoryContent.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8S,GAC3U,4EACA", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/category/%5Bdetail%5D/page.js"], "sourcesContent": ["import HomeLayout from \"@/Layouts/HomeLayout\";\r\nimport { Container } from \"react-bootstrap\";\r\nimport CategoryContent from \"./components/CategoryContent\";\r\nimport { cookies } from \"next/headers\";\r\n\r\nasync function fetchCategoryData(slug, searchKeyword = \"\") {\r\n  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/category?slug=${slug}&key=${searchKeyword}`, { cache: \"no-store\" });\r\n  if (!res.ok) throw new Error(`API error: ${res.status}`);\r\n  return res.json();\r\n}\r\nexport default async function Category({ params }) {\r\n  const slug = params?.detail || \"\";\r\n  const page = parseInt(params?.id || \"1\",);\r\n  const cookieStore = cookies();\r\n  const key = cookieStore.get(\"category-id\")?.value || \"\"; //\r\n  const totalPagesFromClient = cookieStore.get(\"total-pages\")?.value;\r\n\r\n  const isSearch = key?.trim() !== \"\";\r\n  const categoryData = await fetchCategoryData(slug);\r\n  // const totalPages = categoryData?.data?.meta?.total || 1;\r\n  console.log(\"totalPagesFromClient\",totalPagesFromClient)\r\n  const selectedCategory = categoryData?.data?.selected_category || {};\r\n  const environment = process.env.NEXT_PUBLIC_ENVIRONMENT || \"production\"; // Ensure fallback\r\n\r\n  function truncateText(text, maxLength = 160) {\r\n    if (!text || typeof text !== \"string\") return \"\";\r\n\r\n    if (text.length <= maxLength) return text;\r\n\r\n    let cutoff = maxLength - 3;\r\n    if (text[cutoff] === \" \") {\r\n      return text.substring(0, cutoff) + \"...\";\r\n    } else {\r\n      while (cutoff > 0 && text[cutoff] !== \" \") {\r\n        cutoff--;\r\n      }\r\n      return text.substring(0, cutoff) + \"...\";\r\n    }\r\n  }\r\n\r\n  const description = truncateText(selectedCategory?.summary || selectedCategory?.content);\r\n\r\n\r\n  const canonicalLink = page === 1\r\n    ? `https://www.tradereply.com/category${slug ? `/${slug}` : \"\"}`\r\n    : `https://www.tradereply.com/category${slug ? `/${slug}` : \"\"}/page/${page}`;\r\n\r\n  const relNextLink = page < totalPagesFromClient\r\n    ? `https://www.tradereply.com/category${slug ? `/${slug}` : \"\"}/page/${page + 1}`\r\n    : null;\r\n\r\n  const metaArray = {\r\n    title:  `${selectedCategory?.title || \"TradeReply\"} | TradeReply - Expert Trading Insights`,\r\n    description: description,\r\n    og_title:  `${selectedCategory?.title || \"TradeReply\"} | TradeReply - Expert Trading Insights`,\r\n    og_description: description,\r\n    og_site_name: \"TradeReply\",\r\n    twitter_title:  `${selectedCategory?.title || \"TradeReply\"} | TradeReply - Expert Trading Insights`,\r\n    twitter_description: description,\r\n    noindex: isSearch,\r\n    canonical_link: canonicalLink,\r\n    rel_next: relNextLink,\r\n  };\r\n\r\n  const articles = categoryData?.data?.articles || [];\r\n  console.log(\"articles\", articles);\r\n\r\n  const collectionPageSchema = !isSearch ? {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"CollectionPage\",\r\n    \"name\": selectedCategory?.title || \"TradeReply Category\",\r\n    \"description\": selectedCategory?.summary || selectedCategory?.content || \"\",\r\n    \"url\": canonicalLink,\r\n    \"itemListElement\": articles.slice(0, 10).map((article, index) => ({\r\n      \"@type\": \"ListItem\",\r\n      position: index + 1,\r\n      url: `https://www.tradereply.com/${article?.type}/${article?.slug}`\r\n    }))\r\n  } : null;\r\n\r\n  const breadcrumbListSchema = !isSearch ? {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"BreadcrumbList\",\r\n    \"itemListElement\": [\r\n      {\r\n        \"@type\": \"ListItem\",\r\n        position: 1,\r\n        name: \"Home\",\r\n        item: \"https://www.tradereply.com/\"\r\n      },\r\n      {\r\n        \"@type\": \"ListItem\",\r\n        position: 2,\r\n        name: \"Category\",\r\n        item: \"https://www.tradereply.com/category/\"\r\n      },\r\n      {\r\n        \"@type\": \"ListItem\",\r\n        position: 3,\r\n        name: selectedCategory?.title || \"Category\",\r\n        item: `https://www.tradereply.com/category/${slug}/`\r\n      },\r\n      {\r\n        \"@type\": \"ListItem\",\r\n        position: 4,\r\n        name: `Page ${page}`,\r\n        item: canonicalLink\r\n      }\r\n    ]\r\n  } : null;\r\n\r\n  return (\r\n    <>\r\n      {!isSearch && (\r\n        <>\r\n          {collectionPageSchema && (\r\n            <script type=\"application/ld+json\" dangerouslySetInnerHTML={{ __html: JSON.stringify(collectionPageSchema) }} />\r\n          )}\r\n          {breadcrumbListSchema && (\r\n            <script type=\"application/ld+json\" dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbListSchema) }} />\r\n          )}\r\n        </>\r\n      )}\r\n      <CategoryContent\r\n        initialListingAllCategories={categoryData?.data?.allcategories}\r\n        initialAllCategoryArticles={categoryData?.data?.articles}\r\n        initialCategoryMeta={categoryData?.data?.meta}\r\n        initialSelectedCategory={categoryData?.data?.selected_category}\r\n        currentPage={page}\r\n        keyWord={key}\r\n        metaArray={metaArray} \r\n      />\r\n    </>\r\n  );\r\n\r\n}\r\n\r\n// export async function generateMetadata({ params }) {\r\n//   const slug = params?.detail || \"\";\r\n//   const categoryData = await fetchCategoryData(slug);\r\n//   const selectedCategory = categoryData?.data?.selected_category || {};\r\n//   const environment = process.env.NEXT_PUBLIC_ENVIRONMENT || \"production\"; // Ensure fallback\r\n\r\n//   function truncateText(text, maxLength = 160) {\r\n//     if (!text || typeof text !== \"string\") return \"\";\r\n\r\n//     if (text.length <= maxLength) return text;\r\n\r\n//     let cutoff = maxLength - 3;\r\n//     if (text[cutoff] === \" \") {\r\n//       return text.substring(0, cutoff) + \"...\";\r\n//     } else {\r\n//       while (cutoff > 0 && text[cutoff] !== \" \") {\r\n//         cutoff--;\r\n//       }\r\n//       return text.substring(0, cutoff) + \"...\";\r\n//     }\r\n//   }\r\n\r\n//   const description = truncateText(selectedCategory?.summary || selectedCategory?.content);\r\n\r\n//   return {\r\n//     title: `${selectedCategory?.title || \"TradeReply\"} | TradeReply - Expert Trading Insights`,\r\n//     description,\r\n//     openGraph: {\r\n//       title: `${selectedCategory?.title || \"TradeReply\"} | TradeReply - Expert Trading Insights`,\r\n//       description,\r\n//       images: [{\r\n//         url: \"https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png\",\r\n//         width: 1200,\r\n//         height: 630,\r\n//       }],\r\n//     },\r\n//     twitter: {\r\n//       title: `${selectedCategory?.title || \"TradeReply\"} | TradeReply - Expert Trading Insights`,\r\n//       description,\r\n//       site: \"@JoinTradeReply\",\r\n//       images: [\"https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png\"],\r\n//     },\r\n//     icons: {\r\n//       icon: [\r\n//         {\r\n//           url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.ico`,\r\n//           type: \"image/x-icon\",\r\n//         },\r\n//         {\r\n//           url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.svg`,\r\n//           type: \"image/svg+xml\",\r\n//         },\r\n//       ],\r\n//     },\r\n//   };\r\n// }\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;;;;;;AAEA,eAAe,kBAAkB,IAAI,EAAE,gBAAgB,EAAE;IACvD,MAAM,MAAM,MAAM,MAAM,6DAAwC,sBAAsB,EAAE,KAAK,KAAK,EAAE,eAAe,EAAE;QAAE,OAAO;IAAW;IACzI,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,IAAI,MAAM,EAAE;IACvD,OAAO,IAAI,IAAI;AACjB;AACe,eAAe,SAAS,EAAE,MAAM,EAAE;IAC/C,MAAM,OAAO,QAAQ,UAAU;IAC/B,MAAM,OAAO,SAAS,QAAQ,MAAM;IACpC,MAAM,cAAc,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,MAAM,YAAY,GAAG,CAAC,gBAAgB,SAAS,IAAI,EAAE;IAC3D,MAAM,uBAAuB,YAAY,GAAG,CAAC,gBAAgB;IAE7D,MAAM,WAAW,KAAK,WAAW;IACjC,MAAM,eAAe,MAAM,kBAAkB;IAC7C,2DAA2D;IAC3D,QAAQ,GAAG,CAAC,wBAAuB;IACnC,MAAM,mBAAmB,cAAc,MAAM,qBAAqB,CAAC;IACnE,MAAM,cAAc,2CAAuC,cAAc,kBAAkB;IAE3F,SAAS,aAAa,IAAI,EAAE,YAAY,GAAG;QACzC,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU,OAAO;QAE9C,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;QAErC,IAAI,SAAS,YAAY;QACzB,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK;YACxB,OAAO,KAAK,SAAS,CAAC,GAAG,UAAU;QACrC,OAAO;YACL,MAAO,SAAS,KAAK,IAAI,CAAC,OAAO,KAAK,IAAK;gBACzC;YACF;YACA,OAAO,KAAK,SAAS,CAAC,GAAG,UAAU;QACrC;IACF;IAEA,MAAM,cAAc,aAAa,kBAAkB,WAAW,kBAAkB;IAGhF,MAAM,gBAAgB,SAAS,IAC3B,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC,EAAE,MAAM,GAAG,IAAI,GAC9D,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC,EAAE,MAAM,GAAG,GAAG,MAAM,EAAE,MAAM;IAE/E,MAAM,cAAc,OAAO,uBACvB,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC,EAAE,MAAM,GAAG,GAAG,MAAM,EAAE,OAAO,GAAG,GAC/E;IAEJ,MAAM,YAAY;QAChB,OAAQ,GAAG,kBAAkB,SAAS,aAAa,uCAAuC,CAAC;QAC3F,aAAa;QACb,UAAW,GAAG,kBAAkB,SAAS,aAAa,uCAAuC,CAAC;QAC9F,gBAAgB;QAChB,cAAc;QACd,eAAgB,GAAG,kBAAkB,SAAS,aAAa,uCAAuC,CAAC;QACnG,qBAAqB;QACrB,SAAS;QACT,gBAAgB;QAChB,UAAU;IACZ;IAEA,MAAM,WAAW,cAAc,MAAM,YAAY,EAAE;IACnD,QAAQ,GAAG,CAAC,YAAY;IAExB,MAAM,uBAAuB,CAAC,WAAW;QACvC,YAAY;QACZ,SAAS;QACT,QAAQ,kBAAkB,SAAS;QACnC,eAAe,kBAAkB,WAAW,kBAAkB,WAAW;QACzE,OAAO;QACP,mBAAmB,SAAS,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,SAAS,QAAU,CAAC;gBAChE,SAAS;gBACT,UAAU,QAAQ;gBAClB,KAAK,CAAC,2BAA2B,EAAE,SAAS,KAAK,CAAC,EAAE,SAAS,MAAM;YACrE,CAAC;IACH,IAAI;IAEJ,MAAM,uBAAuB,CAAC,WAAW;QACvC,YAAY;QACZ,SAAS;QACT,mBAAmB;YACjB;gBACE,SAAS;gBACT,UAAU;gBACV,MAAM;gBACN,MAAM;YACR;YACA;gBACE,SAAS;gBACT,UAAU;gBACV,MAAM;gBACN,MAAM;YACR;YACA;gBACE,SAAS;gBACT,UAAU;gBACV,MAAM,kBAAkB,SAAS;gBACjC,MAAM,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YACtD;YACA;gBACE,SAAS;gBACT,UAAU;gBACV,MAAM,CAAC,KAAK,EAAE,MAAM;gBACpB,MAAM;YACR;SACD;IACH,IAAI;IAEJ,qBACE;;YACG,CAAC,0BACA;;oBACG,sCACC,8OAAC;wBAAO,MAAK;wBAAsB,yBAAyB;4BAAE,QAAQ,KAAK,SAAS,CAAC;wBAAsB;;;;;;oBAE5G,sCACC,8OAAC;wBAAO,MAAK;wBAAsB,yBAAyB;4BAAE,QAAQ,KAAK,SAAS,CAAC;wBAAsB;;;;;;;;0BAIjH,8OAAC,iLAAA,CAAA,UAAe;gBACd,6BAA6B,cAAc,MAAM;gBACjD,4BAA4B,cAAc,MAAM;gBAChD,qBAAqB,cAAc,MAAM;gBACzC,yBAAyB,cAAc,MAAM;gBAC7C,aAAa;gBACb,SAAS;gBACT,WAAW;;;;;;;;AAKnB,EAEA,uDAAuD;CACvD,uCAAuC;CACvC,wDAAwD;CACxD,0EAA0E;CAC1E,gGAAgG;CAEhG,mDAAmD;CACnD,wDAAwD;CAExD,iDAAiD;CAEjD,kCAAkC;CAClC,kCAAkC;CAClC,kDAAkD;CAClD,eAAe;CACf,qDAAqD;CACrD,oBAAoB;CACpB,UAAU;CACV,kDAAkD;CAClD,QAAQ;CACR,MAAM;CAEN,8FAA8F;CAE9F,aAAa;CACb,kGAAkG;CAClG,mBAAmB;CACnB,mBAAmB;CACnB,oGAAoG;CACpG,qBAAqB;CACrB,mBAAmB;CACnB,0FAA0F;CAC1F,uBAAuB;CACvB,uBAAuB;CACvB,YAAY;CACZ,SAAS;CACT,iBAAiB;CACjB,oGAAoG;CACpG,qBAAqB;CACrB,iCAAiC;CACjC,6FAA6F;CAC7F,SAAS;CACT,eAAe;CACf,gBAAgB;CAChB,YAAY;CACZ,iGAAiG;CACjG,kCAAkC;CAClC,aAAa;CACb,YAAY;CACZ,iGAAiG;CACjG,mCAAmC;CACnC,aAAa;CACb,WAAW;CACX,SAAS;CACT,OAAO;CACP,IAAI", "debugId": null}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}