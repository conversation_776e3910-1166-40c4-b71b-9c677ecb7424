'use client';
import { Col, Row } from "react-bootstrap";
import React from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import CommonBlackCard from "@/Components/common/Account/CommonBlackCard";
import MetaHead from "@/Seo/Meta/MetaHead";
import { RightArrowIcon } from "@/assets/svgIcons/SvgIcon";

export default function Subscriptions() {
    const metaArray = {
        noindex: true,
        title: "Account Subscriptions | Manage Plans | TradeReply",
        description: "Check your subscription status on TradeReply.com. View your current plan, manage upgrades, and review billing details.",
        canonical_link: "https://www.tradereply.com/account/subscriptions",
        og_site_name: "TradeReply",
        og_title: "Subscription Status | Manage Plans | TradeReply",
        og_description: "View and manage your subscription plans on TradeReply. Upgrade, downgrade, or cancel your subscription with ease.",
        twitter_title: "Subscription Status | Manage Plans | TradeReply",
        twitter_description: "View and manage your subscription plans on TradeReply. Upgrade, downgrade, or cancel your subscription with ease.",
    };
    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="subscriptions_sec">
                    <SidebarHeading title="Subscriptions" />
                    <Row className="mb-4 mb-lg-4">
                        <Col xs={12}>
                            <CommonBlackCard
                                title="Subscriptions"
                                Linktext="Change Subscription"
                                Linkicon={<RightArrowIcon />}
                                className="account_card"
                            >
                                <div className="account_card_subscription">
                                    <ul className="account_card_subscription_list">
                                        <li>
                                            <h6>
                                                Trade<span className="blue_text">Reply</span>
                                            </h6>
                                        </li>
                                        <li>
                                            <h6>
                                                <span>Essential Plan</span>
                                            </h6>
                                        </li>
                                        <li>
                                            <h6 className="yellow_text">SUSPENDED</h6>
                                        </li>
                                        <li>
                                            <h6>MAY 3 , 2024</h6>
                                        </li>
                                        <li className="d-none d-md-block"></li>
                                        <li className="d-none d-md-block"></li>
                                        <li>
                                            <p>Account Status</p>
                                        </li>
                                        <li>
                                            <p>Renewal</p>
                                        </li>
                                    </ul>
                                </div>
                            </CommonBlackCard>
                        </Col>
                    </Row>
                </div>
            </AccountLayout>
        </>
    )
}
