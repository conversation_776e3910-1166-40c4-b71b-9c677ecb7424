module.exports = {

"[project]/.next-internal/server/app/(Home)/category/[detail]/page/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[project]/src/app/layout.js [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/layout.js [app-rsc] (ecmascript)"));
}}),
"[project]/src/app/not-found.js [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/not-found.js [app-rsc] (ecmascript)"));
}}),
"[project]/src/Layouts/HomeLayout.js (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/Layouts/HomeLayout.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/Layouts/HomeLayout.js <module evaluation>", "default");
}}),
"[project]/src/Layouts/HomeLayout.js (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/Layouts/HomeLayout.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/Layouts/HomeLayout.js", "default");
}}),
"[project]/src/Layouts/HomeLayout.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Layouts$2f$HomeLayout$2e$js__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/Layouts/HomeLayout.js (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Layouts$2f$HomeLayout$2e$js__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/Layouts/HomeLayout.js (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Layouts$2f$HomeLayout$2e$js__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/app/(Home)/category/[detail]/components/CategoryContent.js (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/(Home)/category/[detail]/components/CategoryContent.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/(Home)/category/[detail]/components/CategoryContent.js <module evaluation>", "default");
}}),
"[project]/src/app/(Home)/category/[detail]/components/CategoryContent.js (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/(Home)/category/[detail]/components/CategoryContent.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/(Home)/category/[detail]/components/CategoryContent.js", "default");
}}),
"[project]/src/app/(Home)/category/[detail]/components/CategoryContent.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$Home$292f$category$2f5b$detail$5d2f$components$2f$CategoryContent$2e$js__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/app/(Home)/category/[detail]/components/CategoryContent.js (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$Home$292f$category$2f5b$detail$5d2f$components$2f$CategoryContent$2e$js__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/app/(Home)/category/[detail]/components/CategoryContent.js (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$Home$292f$category$2f5b$detail$5d2f$components$2f$CategoryContent$2e$js__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/app/(Home)/category/[detail]/page.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Category)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Layouts$2f$HomeLayout$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/Layouts/HomeLayout.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$Home$292f$category$2f5b$detail$5d2f$components$2f$CategoryContent$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/(Home)/category/[detail]/components/CategoryContent.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-rsc] (ecmascript)");
;
;
;
;
;
async function fetchCategoryData(slug, searchKeyword = "") {
    const res = await fetch(`${("TURBOPACK compile-time value", "http://127.0.0.1:8000")}/api/v1/category?slug=${slug}&key=${searchKeyword}`, {
        cache: "no-store"
    });
    if (!res.ok) throw new Error(`API error: ${res.status}`);
    return res.json();
}
async function Category({ params }) {
    const slug = params?.detail || "";
    const page = parseInt(params?.id || "1");
    const cookieStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
    const key = cookieStore.get("category-id")?.value || ""; //
    const totalPagesFromClient = cookieStore.get("total-pages")?.value;
    const isSearch = key?.trim() !== "";
    const categoryData = await fetchCategoryData(slug);
    // const totalPages = categoryData?.data?.meta?.total || 1;
    console.log("totalPagesFromClient", totalPagesFromClient);
    const selectedCategory = categoryData?.data?.selected_category || {};
    const environment = ("TURBOPACK compile-time value", "dev") || "production"; // Ensure fallback
    function truncateText(text, maxLength = 160) {
        if (!text || typeof text !== "string") return "";
        if (text.length <= maxLength) return text;
        let cutoff = maxLength - 3;
        if (text[cutoff] === " ") {
            return text.substring(0, cutoff) + "...";
        } else {
            while(cutoff > 0 && text[cutoff] !== " "){
                cutoff--;
            }
            return text.substring(0, cutoff) + "...";
        }
    }
    const description = truncateText(selectedCategory?.summary || selectedCategory?.content);
    const canonicalLink = page === 1 ? `https://www.tradereply.com/category${slug ? `/${slug}` : ""}` : `https://www.tradereply.com/category${slug ? `/${slug}` : ""}/page/${page}`;
    const relNextLink = page < totalPagesFromClient ? `https://www.tradereply.com/category${slug ? `/${slug}` : ""}/page/${page + 1}` : null;
    const metaArray = {
        title: `${selectedCategory?.title || "TradeReply"} | TradeReply - Expert Trading Insights`,
        description: description,
        og_title: `${selectedCategory?.title || "TradeReply"} | TradeReply - Expert Trading Insights`,
        og_description: description,
        og_site_name: "TradeReply",
        twitter_title: `${selectedCategory?.title || "TradeReply"} | TradeReply - Expert Trading Insights`,
        twitter_description: description,
        noindex: isSearch,
        canonical_link: canonicalLink,
        rel_next: relNextLink
    };
    const articles = categoryData?.data?.articles || [];
    console.log("articles", articles);
    const collectionPageSchema = !isSearch ? {
        "@context": "https://schema.org",
        "@type": "CollectionPage",
        "name": selectedCategory?.title || "TradeReply Category",
        "description": selectedCategory?.summary || selectedCategory?.content || "",
        "url": canonicalLink,
        "itemListElement": articles.slice(0, 10).map((article, index)=>({
                "@type": "ListItem",
                position: index + 1,
                url: `https://www.tradereply.com/${article?.type}/${article?.slug}`
            }))
    } : null;
    const breadcrumbListSchema = !isSearch ? {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                position: 1,
                name: "Home",
                item: "https://www.tradereply.com/"
            },
            {
                "@type": "ListItem",
                position: 2,
                name: "Category",
                item: "https://www.tradereply.com/category/"
            },
            {
                "@type": "ListItem",
                position: 3,
                name: selectedCategory?.title || "Category",
                item: `https://www.tradereply.com/category/${slug}/`
            },
            {
                "@type": "ListItem",
                position: 4,
                name: `Page ${page}`,
                item: canonicalLink
            }
        ]
    } : null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            !isSearch && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    collectionPageSchema && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("script", {
                        type: "application/ld+json",
                        dangerouslySetInnerHTML: {
                            __html: JSON.stringify(collectionPageSchema)
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/app/(Home)/category/[detail]/page.js",
                        lineNumber: 117,
                        columnNumber: 13
                    }, this),
                    breadcrumbListSchema && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("script", {
                        type: "application/ld+json",
                        dangerouslySetInnerHTML: {
                            __html: JSON.stringify(breadcrumbListSchema)
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/app/(Home)/category/[detail]/page.js",
                        lineNumber: 120,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$Home$292f$category$2f5b$detail$5d2f$components$2f$CategoryContent$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                initialListingAllCategories: categoryData?.data?.allcategories,
                initialAllCategoryArticles: categoryData?.data?.articles,
                initialCategoryMeta: categoryData?.data?.meta,
                initialSelectedCategory: categoryData?.data?.selected_category,
                currentPage: page,
                keyWord: key,
                metaArray: metaArray
            }, void 0, false, {
                fileName: "[project]/src/app/(Home)/category/[detail]/page.js",
                lineNumber: 124,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
} // export async function generateMetadata({ params }) {
 //   const slug = params?.detail || "";
 //   const categoryData = await fetchCategoryData(slug);
 //   const selectedCategory = categoryData?.data?.selected_category || {};
 //   const environment = process.env.NEXT_PUBLIC_ENVIRONMENT || "production"; // Ensure fallback
 //   function truncateText(text, maxLength = 160) {
 //     if (!text || typeof text !== "string") return "";
 //     if (text.length <= maxLength) return text;
 //     let cutoff = maxLength - 3;
 //     if (text[cutoff] === " ") {
 //       return text.substring(0, cutoff) + "...";
 //     } else {
 //       while (cutoff > 0 && text[cutoff] !== " ") {
 //         cutoff--;
 //       }
 //       return text.substring(0, cutoff) + "...";
 //     }
 //   }
 //   const description = truncateText(selectedCategory?.summary || selectedCategory?.content);
 //   return {
 //     title: `${selectedCategory?.title || "TradeReply"} | TradeReply - Expert Trading Insights`,
 //     description,
 //     openGraph: {
 //       title: `${selectedCategory?.title || "TradeReply"} | TradeReply - Expert Trading Insights`,
 //       description,
 //       images: [{
 //         url: "https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png",
 //         width: 1200,
 //         height: 630,
 //       }],
 //     },
 //     twitter: {
 //       title: `${selectedCategory?.title || "TradeReply"} | TradeReply - Expert Trading Insights`,
 //       description,
 //       site: "@JoinTradeReply",
 //       images: ["https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png"],
 //     },
 //     icons: {
 //       icon: [
 //         {
 //           url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.ico`,
 //           type: "image/x-icon",
 //         },
 //         {
 //           url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.svg`,
 //           type: "image/svg+xml",
 //         },
 //       ],
 //     },
 //   };
 // }
}}),
"[project]/src/app/(Home)/category/[detail]/page.js [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/(Home)/category/[detail]/page.js [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=_edc8b995._.js.map