import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { login } from "@/utils/auth";
import Cookies from "js-cookie";
import toast from "react-hot-toast";

export const loginUser = createAsyncThunk(
  "auth/login",
  async ({ email, password, captchaToken }, { rejectWithValue }) => {
    try {
      const credentials = { email, password, captchaToken };
      const response = await login(credentials);

      // console.log('slice response',response)
      // console.log('slice response.data',response.data)

      // if (response?.user) {
      //   localStorage.setItem("user", JSON.stringify(response.user));
      //   Cookies.set("authToken", response.token, { expires: 1 });
      // }

      // if (response?.captcha_required) {
      //   sessionStorage.setItem("captcha_required", "true");
      // } else {
      //   sessionStorage.removeItem("captcha_required");
      // }
      if (response?.data?.user) {
        localStorage.setItem("user", JSON.stringify(response.data.user));
        Cookies.set("authToken", response.data.token);
        if(response?.data?.active_subscription) {
            Cookies.set("active_subscription", response?.data?.active_subscription, { expires: 1 });
        }
        // const subscriptionId = response.data.user.subscription_id;
        // if (subscriptionId) {
        //   Cookies.set("subscription_id", subscriptionId, { expires: 1 });
        // }
      }


      if (response?.captcha_required) {
        sessionStorage.setItem("captcha_required", "true");
      } else {
        sessionStorage.removeItem("captcha_required");
      }

      if (!response.success) {
        return rejectWithValue(response);
      }

      return response;
    } catch (error) {
      console.error("Login error message authslice:", error.response);

      const errorMessage = error.response?.data?.message || "An error occurred.";
      toast.error(errorMessage);

      return rejectWithValue({
        success: false,
        message: errorMessage,
        errors: error.response?.data?.errors || [],
        captcha_required:
          error.response?.data?.type === 'captcha'
            ? error.response?.data?.state
            : false,
      });
    }
  }
);


const authSlice = createSlice({
  name: "auth",
  initialState: {
    user: null,
    token: null,
    loading: false,
    error: null,
  },
  reducers: {
    logoutUser: (state) => {
      state.user = null;
      state.token = null;
      Cookies.remove("authToken");
      localStorage.clear();
      sessionStorage.clear();
    },
    setUser: (state, action) => {
      state.user = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(loginUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { logoutUser, setUser } = authSlice.actions;
export const getUser = (state) => state.auth.user;
export default authSlice.reducer;