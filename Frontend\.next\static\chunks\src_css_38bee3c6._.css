/* [project]/src/css/common/CommonSearch.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

.commonSearch {
  display: flex;
  position: relative;
  align-items: center;
}

.commonSearch.searchBtn .form-control {
  padding: .5rem 1rem;
  background-color: #fff;
  color: #000;
  border: 0;
  border-top-left-radius: 10rem;
  border-bottom-left-radius: 10rem;
  min-height: 50px;
  width: calc(100% - 54px);
  min-width: auto;
  font-weight: 600;
}

.commonSearch.searchBtn .form-control::placeholder {
  color: #c5c5d5;
  opacity: 1;
}

.commonSearch.searchBtn .form-control:focus {
  box-shadow: none;
  outline: 0;
  background-color: #fff;
  color: #000;
}

.commonSearch .form-control {
  padding: .5rem 1rem;
  padding-left: 50px;
  background-color: #ffffff4d;
  color: #fff;
  border: 0;
  border-radius: 15px;
  min-height: 70px;
  width: auto;
  width: 400px;
  font-size: 1.25rem;
  appearance: none;
  -webkit-appearance: none;
}

@media (width <= 991px) {
  .commonSearch .form-control {
    font-size: 16px;
    min-height: 56px;
    padding-left: 40px;
  }
}

.commonSearch .form-control:hover {
  appearance: none;
}

.commonSearch .form-control::placeholder {
  color: #fffc;
  opacity: 1;
}

.commonSearch .form-control:disabled {
  background-color: #0000;
}

.commonSearch .form-control:focus {
  box-shadow: none;
  outline: 0;
  background-color: #ffffff4d;
  color: #fff;
  border: 0;
}

.commonSearch .onlyIcon {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
}

@media (width <= 991px) {
  .commonSearch .onlyIcon {
    left: 15px;
  }
}

.commonSearch .btnIcon {
  cursor: pointer;
  width: 54px;
  min-height: 50px;
  background-color: #00adef;
  border-top-right-radius: 10rem;
  border-bottom-right-radius: 10rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all .3s ease-in-out;
}

.commonSearch .btnIcon:hover {
  background-color: #fea500;
}


/* [project]/src/css/common/CustomPagination.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.customPagination .pagination .page-item .page-link {
  background-color: #0000;
  border: 0;
  color: #00adef;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  border: 1px solid #0000;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1.25rem;
}

.customPagination .pagination .page-item .page-link:focus {
  box-shadow: none;
}

.customPagination .pagination .page-item:last-child .page-link, .customPagination .pagination .page-item:first-child .page-link {
  border: 0;
  background-color: #00adef;
  border-radius: 10rem;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.customPagination .pagination .page-item:last-child .page-link span, .customPagination .pagination .page-item:first-child .page-link span {
  display: flex;
}

.customPagination .pagination .page-item:last-child {
  margin-left: 1rem;
}

.customPagination .pagination .page-item:first-child {
  margin-right: 1rem;
}

.customPagination .pagination .page-item.active .page-link {
  text-decoration: underline;
}

.customPagination .pagination .page-item:hover .page-link {
  color: #0099d1;
  text-decoration: underline;
}

.customPagination .pagination .page-item:hover:last-child .page-link, .customPagination .pagination .page-item:hover:first-child .page-link {
  background-color: #0099d1;
}

.customPagination .pagination .page-item.disabled, .customPagination .pagination .page-item:disabled {
  border-radius: 10rem;
  background-color: #414c60;
  pointer-events: none;
}

.customPagination .pagination .page-item.disabled .page-link, .customPagination .pagination .page-item:disabled .page-link {
  background-color: #414c60;
  opacity: .6;
  cursor: not-allowed;
}

.customPagination .pagination .prevArrow {
  transform: rotate(-180deg);
}

.paginate-arrows {
  background-color: #00adef;
  color: #fff;
  border: none;
  padding: 0;
  cursor: pointer;
  font-size: 1.2rem;
  min-width: 30px;
  min-height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10rem;
  position: relative;
}

.pagination a, .pagination .link {
  color: #00adef !important;
  cursor: pointer !important;
}

.pagination .active {
  text-decoration: underline;
}


/* [project]/src/css/Home/Blog.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.blog {
  position: relative;
  padding: 5rem 0;
}

.blog_cards {
  margin-bottom: 50px !important;
}

@media (width <= 767px) {
  .blog_cards {
    margin-bottom: 20px !important;
  }
}

@media (width <= 575px) {
  .blog_cards {
    margin-bottom: 10px !important;
  }
}

.blog_cards .slider-container {
  position: relative;
}

.blog_cards .slider-container .slick-slider {
  margin: 0 -15px;
}

.blog_cards .slider-container .slick-slider .slick-list .slick-track {
  display: flex;
}

.blog_cards .slider-container .slick-slider .slick-list .slick-track .slick-slide {
  padding: 0 15px;
  display: flex;
  height: auto;
}

.blog_cards .slider-container .slick-slider .slick-list .slick-track .slick-slide > div {
  width: 100%;
  display: flex;
}

.blog_cards .slider-container .slick-slider .slick-list .slick-track .slick-slide.slick-active {
  padding-top: 0;
}

.blog_postcard {
  position: relative;
  background-color: #032251;
  border-radius: 1.25rem;
  border: 1px solid #ffffff80;
  cursor: pointer;
  height: 100%;
}

@media (width <= 767px) {
  .blog_postcard {
    margin-bottom: 1.25rem;
  }
}

.blog_postcard:hover .blog_postcard_img img {
  transform: scale(1.1);
}

.blog_postcard_img {
  border-top-left-radius: 1.25rem;
  border-top-right-radius: 1.25rem;
  overflow: hidden;
  position: relative;
}

.blog_postcard_img:before {
  content: "";
  background-color: #fff9;
  border-top-left-radius: 1.25rem;
  border-top-right-radius: 1.25rem;
  display: block;
  padding-top: 54.25%;
  width: 100%;
}

.blog_postcard_img_overlay {
  bottom: 0;
  display: block;
  height: 100%;
  left: 0;
  margin-left: auto;
  margin-right: auto;
  overflow: hidden;
  position: absolute;
  right: 0;
  top: 0;
}

.blog_postcard_img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-top-left-radius: 1.25rem;
  border-top-right-radius: 1.25rem;
  transition: all .3s ease-in-out;
}

.blog_postcard_content {
  padding: .65rem 1.25rem 2rem;
}

@media (width <= 1199px) {
  .blog_postcard_content {
    padding: .65rem 1rem;
  }
}

.blog_postcard_content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.5rem;
  letter-spacing: -.1px;
  margin-bottom: .625rem;
  color: #c5c5d5;
}

@media (width <= 1199px) {
  .blog_postcard_content h3 {
    font-size: 1rem;
    line-height: 1.25rem;
  }
}

.blog_postcard_content p {
  font-size: 1.5rem;
  font-weight: 400;
  line-height: 2rem;
  letter-spacing: -.1px;
  color: #fff;
}

@media (width <= 1199px) {
  .blog_postcard_content p {
    font-size: 15px;
    line-height: 23px;
  }
}

@media (width <= 767px) {
  .blog .slider-container .slick-slider .slick-arrow.slick-prev {
    left: -45px !important;
    top: 100px !important;
  }
}

.blog .slider-container .slick-slider .slick-arrow.slick-prev.slick-disabled {
  background-color: #414c60;
  opacity: .7;
}

@media (width <= 767px) {
  .blog .slider-container .slick-slider .slick-arrow.slick-next {
    right: -30px !important;
    top: 100px !important;
  }
}

.blog .slider-container .slick-slider .slick-arrow.slick-next.slick-disabled {
  background-color: #414c60;
  opacity: .7;
}

.recent_post {
  position: relative;
  background-color: #032251;
  border-radius: 1.25rem;
  border: 1px solid #08050580;
  margin-bottom: 30px;
  display: flex;
  flex-wrap: wrap;
  cursor: pointer;
  width: 100%;
}

.recent_post h1 {
  margin-bottom: 50px;
  font-size: 3rem;
  font-weight: 800;
}

@media (width <= 767px) {
  .recent_post h1 {
    margin-bottom: 30px;
  }

  .recent_post {
    margin-bottom: 1.25rem;
  }
}

.recent_post_img {
  border-top-left-radius: 1.25rem;
  border-bottom-left-radius: 1.25rem;
  overflow: hidden;
  position: relative;
  width: 366px;
}

@media (width <= 991px) {
  .recent_post_img {
    width: 40%;
  }
}

@media (width <= 599px) {
  .recent_post_img {
    width: 100%;
    border-top-left-radius: 1.25rem;
    border-top-right-radius: 1.25rem;
    border-bottom-left-radius: 0;
  }

  .recent_post_img:before {
    content: "";
    background-color: #fff0;
    border-top-left-radius: 1.25rem;
    border-top-right-radius: 1.25rem;
    display: block;
    padding-top: 54.25%;
    width: 100%;
  }
}

.recent_post_img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@media (width <= 767px) {
  .recent_post_img img {
    bottom: 0;
    display: block;
    height: 100%;
    left: 0;
    margin-left: auto;
    margin-right: auto;
    overflow: hidden;
    position: absolute;
    right: 0;
    top: 0;
  }
}

.recent_post_content {
  width: calc(100% - 366px);
  padding: 2rem 1.25rem;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

@media (width <= 991px) {
  .recent_post_content {
    width: 60%;
    padding: 1rem 1.25rem;
  }
}

@media (width <= 599px) {
  .recent_post_content {
    width: 100%;
    padding: 2rem 1rem;
  }
}

.recent_post_content small {
  display: block;
}

.recent_post_content h4 {
  margin-block: .625rem;
}

.recent_post_content p, .recent_post_content small {
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 27px;
  color: #c5c5d5;
}

@media (width <= 767px) {
  .recent_post_content p, .recent_post_content small {
    font-size: 1rem;
  }
}

.recent_post_time {
  color: #00adef;
  font-size: 1rem;
  font-weight: 600;
  line-height: 26px;
  display: block;
  margin-top: 10px;
}

@media (width <= 767px) {
  .recent_post_time {
    font-size: .9rem;
  }
}

.recontPostTitle {
  margin-bottom: 50px;
  font-size: 3rem;
  font-weight: 800;
}

@media (width <= 767px) {
  .recontPostTitle {
    font-size: 1.5rem;
    margin-bottom: 30px;
  }
}

.slick-slider .slick-prev, .slick-slider .slick-next {
  top: 50%;
  display: block;
  width: 20px;
  height: 20px;
  padding: 0;
  cursor: pointer;
  color: #0000;
  border: none;
  outline: none;
  background: none;
  position: absolute !important;
  -webkit-transform: translate(0, -50%) !important;
  -ms-transform: translate(0, -50%) !important;
  transform: translateY(-50%) !important;
}


/* [project]/src/css/common/CommonButton.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.btn-style, .btn-primary {
  min-height: 66px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  border-radius: 10rem;
  padding: .5rem 1.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  background-color: #00adef;
  border: 0;
  text-transform: capitalize;
  transition: all .3s ease-in-out;
  min-width: 150px;
  color: #fff;
}

.btn-style span, .btn-primary span {
  line-height: 1;
}

@media (width <= 1599px) {
  .btn-style, .btn-primary {
    min-height: 66px;
  }
}

@media (width <= 1199px) {
  .btn-style, .btn-primary {
    min-height: 56px;
    font-size: 1.125rem;
    font-weight: 500;
  }
}

@media (width <= 767px) {
  .btn-style, .btn-primary {
    min-height: 46px;
    font-size: 1rem;
  }
}

.btn-style:hover, .btn-primary:hover {
  background-color: #0099d1;
  color: #fff;
}

.btn-style.transparent, .btn-primary.transparent {
  background-color: #0000;
  border: none;
}

.btn-style.white-btn, .btn-primary.white-btn {
  background: #fff;
  color: #000;
}

.btn-style.white-btn:hover, .btn-primary.white-btn:hover {
  background: #00adef;
  color: #fff;
}

.btn-style.yellow-btn, .btn-primary.yellow-btn {
  background-color: #fea500;
  color: #fff;
}

.btn-style.yellow-btn:hover, .btn-primary.yellow-btn:hover {
  background-color: #c9870d;
  color: #fff;
}

.btn-style.gray-btn, .btn-primary.gray-btn {
  color: #fff;
  background-color: #5e6165 !important;
}

.btn-style.gray-btn:hover, .btn-primary.gray-btn:hover {
  background-color: #708090;
  color: #fff;
}

.btn-style.gradient-btn, .btn-primary.gradient-btn {
  background: linear-gradient(75deg, #00aeef, #1f5aff 50.31%, #da00ff);
  color: #fff;
}

.btn-style.gradient-btn:hover, .btn-primary.gradient-btn:hover {
  background: linear-gradient(75deg, #0043ff, #1f5aff 50.31%, #da00ff);
  color: #fff;
}

.btn-style.green-btn, .btn-primary.green-btn {
  background-color: #32cd33;
  color: #fff;
}

.btn-style.green-btn:hover, .btn-primary.green-btn:hover {
  background-color: #2bb72b;
  color: #fff;
}

.btn-style.red-btn, .btn-primary.red-btn {
  background-color: #ff696a;
  color: #fff;
}

.btn-style.red-btn:hover, .btn-primary.red-btn:hover {
  background-color: #e65f60;
  color: #fff;
}

.btn-style.border-btn, .btn-primary.border-btn {
  background: none;
  color: #fff;
  border: 1px solid #00adef;
}

.btn-style.border-btn:hover, .btn-primary.border-btn:hover {
  background: #00adef;
  color: #fff;
}

.btn-style .onlyIcon, .btn-primary .onlyIcon {
  margin-right: 15px;
  display: inline-flex;
}

.btn-style:disabled, .btn-style.disabled, .btn-primary:disabled, .btn-primary.disabled {
  background: #c5c5d5;
  color: #fff;
  cursor: not-allowed;
  opacity: 1;
}

:disabled, .disabled {
  background-color: #414c60;
  color: #fff;
  cursor: not-allowed;
  opacity: 1;
}

.white20 {
  background-color: #ffffff1f;
  width: 100%;
}


/* [project]/src/css/common/Header.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

header {
  position: sticky;
  top: 0;
  left: 0;
  z-index: 9998;
}

.home-page .siteHeader {
  border-bottom: 0;
  background-color: #000 !important;
}

@media (width >= 1200px) {
  .home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle.show, .home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle:hover {
    color: #fff;
    background-color: #2a2e39 !important;
  }

  .home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle.show:after, .home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle:hover:after {
    transform: rotate(180deg);
  }
}

.home-page .siteHeader .navMenu .common_dropdown.dropdown.show .dropdown-toggle:after {
  transform: rotate(180deg);
}

@media (width >= 1200px) {
  .home-page .siteHeader .navMenu .common_dropdown .dropdown-menu {
    background-color: #1e222d !important;
  }
}

.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item:hover, .home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.active, .home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item:focus {
  background-color: #2a2e39 !important;
}

.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon {
  font-weight: 700;
  color: #00b9ff;
  transition: none;
  background: linear-gradient(to right, #2a2e39, #1e222d) !important;
}

.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon svg path {
  fill: #00b9ff;
}

.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon:hover, .home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon.active {
  background: #2a2e39 !important;
}

@media (width <= 1199px) {
  .home-page .siteHeader .navbar-collapse .nav-link.white_stroke_icon {
    font-weight: 700;
    color: #00aeef;
    transition: none;
    background: linear-gradient(to right, #000, #2d2d2d) !important;
  }

  .home-page .siteHeader .navbar-collapse .nav-link.white_stroke_icon svg path {
    fill: #00b9ff;
  }

  .home-page .siteHeader .navbar-collapse {
    background-color: #000000e6 !important;
  }
}

@media (width >= 1200px) {
  .home-page .siteHeader .navbar-collapse .nav-link:hover, .home-page .siteHeader .navbar-collapse .nav-link.active, .home-page .siteHeader .navbar-collapse .nav-link:focus {
    color: #fff;
    background-color: #2a2e39 !important;
  }

  .home-page .languageDropdown {
    width: 64px;
  }
}

@media (width >= 1200px) and (width <= 1199px) {
  .home-page .languageDropdown {
    width: 100%;
  }

  .home-page .languageDropdown .common_dropdown {
    width: 100%;
  }
}

@media (width >= 1200px) {
  .home-page .languageDropdown .common_dropdown .nav-link:hover, .home-page .languageDropdown .common_dropdown .nav-link.active, .home-page .languageDropdown .common_dropdown .nav-link:focus {
    color: #fff;
    background-color: #2a2e39 !important;
  }

  .home-page .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon:hover, .home-page .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon.active {
    background: #2a2e39 !important;
  }
}

.siteHeader {
  height: 80px;
  padding: 1rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  background-color: #031940;
  border-bottom: 1px solid #064197;
}

.siteHeader .btn-style {
  min-height: 56px;
  min-width: 169px;
}

@media (width <= 1199px) {
  .siteHeader .btn-style {
    min-height: 40px;
    min-width: 120px;
    padding: 8px 1rem;
    font-size: 14px;
  }
}

@media (width <= 575px) {
  .siteHeader .btn-style {
    min-height: 34px;
    min-width: 80px;
    font-size: 14px;
  }
}

@media (width <= 1199px) {
  .siteHeader {
    z-index: 9999;
    backdrop-filter: none;
  }
}

@media (width <= 767px) {
  .siteHeader {
    padding: .625rem 0;
  }
}

.siteHeader .navbar {
  padding: 0;
  width: 100%;
}

.siteHeader .navbar .brandLogo img {
  max-width: 190px;
  width: 100%;
}

@media (width <= 767px) {
  .siteHeader .navbar .brandLogo img {
    max-width: 150px;
    margin-right: 0;
  }
}

@media (width <= 360px) {
  .siteHeader .navbar .brandLogo img {
    max-width: 120px;
    margin-right: 0;
  }
}

.siteHeader .navbar-collapse {
  height: auto !important;
}

.siteHeader .navbar-collapse .nav-link {
  font-size: 1.25rem;
  font-weight: 400;
  background-color: #0000;
  display: flex;
  align-items: center;
  white-space: nowrap;
  padding: .5rem 1.5rem;
  color: #fff;
}

.siteHeader .navbar-collapse .nav-link:hover, .siteHeader .navbar-collapse .nav-link.active, .siteHeader .navbar-collapse .nav-link:focus {
  color: #00adef;
}

@media (width >= 1200px) {
  .siteHeader .navbar-collapse .nav-link:hover, .siteHeader .navbar-collapse .nav-link.active, .siteHeader .navbar-collapse .nav-link:focus {
    color: #fff;
    background-color: #283f67 !important;
  }

  .siteHeader .navbar-collapse .nav-link {
    margin: 0 3px;
  }
}

@media (width <= 1199px) {
  .siteHeader .navbar-collapse .nav-link {
    padding: 1.25rem 0;
    border-bottom: 1px solid #fff3;
    font-size: 1.125rem;
  }

  .siteHeader .navbar-collapse .nav-link img {
    width: 22px;
  }

  .siteHeader .navbar-collapse .nav-link.white_stroke_icon {
    font-weight: 700;
    background: linear-gradient(to right, #031940, #283f67);
    color: #00aeef;
    transition: none;
  }

  .siteHeader .navbar-collapse .nav-link.white_stroke_icon svg path {
    fill: #00b9ff;
  }

  .siteHeader .navbar-collapse {
    position: fixed;
    left: -350px;
    top: 0;
    background-color: #031940e6;
    backdrop-filter: blur(5px);
    width: 350px;
    padding: 1.25rem 1rem;
    display: block;
    transition: all .2s ease-in-out;
    z-index: 9999;
    padding: 0;
    height: 100vh !important;
  }

  .siteHeader .navbar-collapse a {
    display: flex;
    justify-content: flex-start;
    text-align: left;
  }

  .siteHeader .navbar-collapse.show {
    left: 0;
    height: 100vh;
  }

  .siteHeader .navbar-collapse .navMenu {
    padding: 20px;
    height: calc(100vh - 90px);
    overflow-y: auto;
  }
}

@media (width <= 767px) {
  .siteHeader .navbar-collapse {
    left: -100%;
    width: 100%;
  }
}

.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle {
  border-radius: 0;
  padding: .5rem 1.5rem !important;
}

@media (width <= 1199px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle {
    border-bottom: 1px solid #fff3;
    width: 100%;
    padding: 1.25rem 0 !important;
  }
}

.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle:after {
  display: block;
  background-image: url("https://cdn.tradereply.com/dev/site-assets/icons/tradereply-drop-arrow.svg");
  background-repeat: no-repeat;
  background-size: 1.15rem;
  background-position: center;
  width: 1.15rem;
  height: 1.15rem;
  border: 0;
  transition: all .3s ease-in-out;
  margin-left: 1rem;
}

@media (width <= 1199px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle:after {
    margin-left: 0;
    position: absolute;
    right: 0;
  }
}

@media (width >= 1200px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle.show, .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle:hover {
    background-color: #283f67;
    color: #fff;
  }

  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle.show:after, .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle:hover:after {
    transform: rotate(180deg);
  }
}

.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown.show .dropdown-toggle:after {
  transform: rotate(180deg);
}

@media screen and (width <= 1199px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-menu {
    position: static;
    border: 0;
    background-color: #0000;
    padding: 0;
  }
}

.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-menu .nav-link {
  padding: .875rem 1.5rem;
  align-items: start;
  font-weight: 400 !important;
}

@media screen and (width <= 1199px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-menu .nav-link {
    padding: .875rem 1rem;
  }
}

.siteHeader .navbar .navbar-toggler {
  background-color: #0000;
  margin-left: 0;
  padding: 0;
  position: relative;
  width: 24px;
  height: 18px;
}

.siteHeader .navbar .navbar-toggler:focus {
  box-shadow: none;
}

@media (width <= 1199px) {
  .siteHeader .navbar .navbar-toggler {
    margin-right: 13px;
  }
}

@media (width <= 767px) {
  .siteHeader .navbar .navbar-toggler {
    margin-right: 13px;
  }
}

.siteHeader .navbar .navbar-toggler:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 24px;
  background-color: #fff;
  height: 2px;
  transition: all .3s ease-in-out;
}

.siteHeader .navbar .navbar-toggler:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 24px;
  background-color: #fff;
  height: 2px;
  transition: all .3s ease-in-out;
}

.siteHeader .navbar .navbar-toggler .navbar-toggler-icon {
  background-image: none;
  height: 2px;
  background-color: #fff;
  width: 24px;
  transition: all .3s ease-in-out;
  display: flex;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle {
  color: #fff;
  border: 0;
  border-radius: .625rem;
  font-size: 1.25rem;
  padding: 0;
  display: flex;
  align-items: center;
  padding: .5rem .2rem !important;
}

@media (width <= 991px) {
  .siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle {
    font-size: 1.125rem;
  }
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle:after {
  display: none;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle.show svg path {
  fill: #00adef;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu {
  border-radius: .625rem;
  border: 1px solid #ffffff4d;
  min-width: 200px;
  position: absolute;
  top: 45px;
}

@media screen and (width <= 1199px) {
  .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu {
    position: static;
    padding: 0;
    min-width: 100%;
  }
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item {
  font-size: 1.125rem;
  font-weight: 600;
  padding: .625rem 1rem;
  color: #fff;
}

@media (width <= 991px) {
  .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item {
    font-size: 1rem;
  }
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item svg, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item img {
  margin-right: 10px;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item:hover, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.active, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item:focus {
  background: #283f67;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon {
  font-weight: 700;
  background: linear-gradient(to right, #283f67, #031940);
  color: #00b9ff;
  transition: none;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon svg path {
  fill: #00b9ff;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon:hover, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon.active {
  background: #283f67 !important;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu.show svg, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu.show img {
  width: 18px;
}

@media screen and (width <= 1199px) {
  .siteHeader .navbar .openmenuSidebar {
    border-bottom: 1px solid #ffffff80;
    padding: 30px 15px;
  }

  .siteHeader .navbar .openmenuSidebar .brandLogo {
    padding: 0;
  }

  .siteHeader .navbar .openmenuSidebar .brandLogo img {
    max-width: 150px;
  }

  .siteHeader .navbar .openmenuSidebar .navbar-toggler {
    position: absolute;
    right: 15px;
  }
}

.siteHeader.openmenu .navbar .navbar-toggler:after {
  transform: rotate(45deg)translate(-5px, -5px);
  background-color: #fff;
}

.siteHeader.openmenu .navbar .navbar-toggler:before {
  transform: rotate(-45deg)translate(-5px, 5px);
  background-color: #fff;
}

.siteHeader.openmenu .navbar .navbar-toggler .navbar-toggler-icon {
  opacity: 0;
}

.siteHeader .user_icon img {
  width: 26px;
  height: 26px;
}

@media screen and (width <= 767px) {
  .siteHeader .sidebar_backdrop {
    display: none;
  }
}

.languageDropdown {
  width: 64px;
}

@media (width <= 1199px) {
  .languageDropdown {
    width: 100%;
  }

  .languageDropdown .common_dropdown {
    width: 100%;
  }
}

.languageDropdown .common_dropdown .nav-link:hover, .languageDropdown .common_dropdown .nav-link.active, .languageDropdown .common_dropdown .nav-link:focus {
  color: #fff;
  background-color: #283f67 !important;
}

.languageDropdown .common_dropdown.dropdown .dropdown-toggle {
  color: #fff;
  border: 0;
  font-size: 1.25rem;
  padding: 0;
  display: flex;
  align-items: center;
  border-radius: 0 !important;
}

@media (width <= 991px) {
  .languageDropdown .common_dropdown.dropdown .dropdown-toggle {
    font-size: 1rem;
  }
}

.languageDropdown .common_dropdown.dropdown .dropdown-toggle svg {
  margin-right: 10px;
}

.languageDropdown .common_dropdown.dropdown .dropdown-toggle:focus, .languageDropdown .common_dropdown.dropdown .dropdown-toggle:hover {
  background-color: #0000 !important;
}

@media (width <= 1199px) {
  .languageDropdown .common_dropdown.dropdown .dropdown-toggle {
    width: 100%;
  }
}

.languageDropdown .globalIcon .icon {
  transition: opacity .3s;
}

.languageDropdown .globalIcon .blue {
  display: none;
}

.languageDropdown .nav-item:hover .globalIcon .black, .languageDropdown .nav-item.show .globalIcon .black {
  display: none;
}

.languageDropdown .nav-item:hover .globalIcon .blue, .languageDropdown .nav-item.show .globalIcon .blue {
  display: block;
}

.userDropdown.common_dropdown.dropdown .dropdown-toggle .user_name {
  display: none;
}

@media screen and (width <= 1199px) {
  .userDropdown.common_dropdown.dropdown .dropdown-toggle .user_name {
    display: block;
    padding-left: 10px;
    font-size: 18px;
  }

  .userDropdown.common_dropdown.dropdown .dropdown-toggle .user_name svg {
    width: 26px;
    height: 26px;
  }

  .userDropdown.common_dropdown.dropdown .dropdown-toggle {
    border-bottom: 0 !important;
  }
}

.userDropdown.common_dropdown.dropdown .dropdown-toggle:hover {
  background-color: #0000 !important;
}

@media (width <= 1199px) {
  .brandLogo {
    display: flex;
  }

  .brandLogo img {
    max-width: 150px;
  }
}

@media (width <= 767px) {
  .brandLogo img {
    max-width: 110px;
  }
}

@media (width <= 359px) {
  .brandLogo img {
    max-width: 100px;
  }
}

.sidebar_backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 1000;
  background-color: #0003;
  transition: all .2s ease-in-out;
}

.image_color_to_white {
  filter: brightness(0) invert();
}

@media (width >= 1200px) {
  .nav-link:hover, .nav-link.active, .nav-link:focus {
    color: #fff;
    background-color: #2a2e39 !important;
  }
}


/* [project]/src/css/common/Footer.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.site_footer {
  background-color: #000;
}

.site_footer_inner {
  padding: 70px 0;
}

@media screen and (width <= 991px) {
  .site_footer_inner {
    padding: 40px 0;
  }
}

.site_footer_logo img {
  width: 200px;
}

.site_footer_content p {
  color: #ffffffa6;
  font-size: 18px;
  font-weight: 600;
  line-height: 26px;
  letter-spacing: -.1px;
  margin-top: 20px;
}

@media screen and (width <= 991px) {
  .site_footer_content p {
    font-size: 16px;
  }
}

@media screen and (width <= 767px) {
  .site_footer_links {
    margin-top: 20px;
  }
}

.site_footer_links h4 {
  color: #c5c5d5;
  margin-bottom: 1.25rem;
  font-size: 1.65rem;
  line-height: 35px;
  font-weight: 600;
}

@media screen and (width <= 991px) {
  .site_footer_links h4 {
    font-size: 18px;
  }
}

.site_footer_links ul li a {
  font-size: 20px;
  font-weight: 600;
  line-height: 24.5px;
  letter-spacing: -.1px;
  color: #fff;
  transition: all .3s ease-in-out;
  padding-bottom: 10px;
}

@media screen and (width <= 991px) {
  .site_footer_links ul li a {
    font-size: 16px;
  }
}

.site_footer_links ul li a:hover, .site_footer_links ul li a.active {
  color: #00adef;
}

.site_footer_copyright {
  padding: 1.25rem 0;
  border-top: 1px solid #fff;
}

.site_footer_copyright p {
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  line-height: 26px;
  letter-spacing: -.1px;
}

@media screen and (width <= 991px) {
  .site_footer_copyright p {
    font-size: 16px;
  }
}


/* [project]/src/css/Home/Category.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.categorySec .container {
  max-width: 1080px;
}

.categorySec_heading h1 {
  font-weight: 800;
  font-size: 3rem !important;
}

.categorySec_heading p {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 28px;
  letter-spacing: -.1px;
  margin: 30px 0;
}

@media (width <= 991px) {
  .categorySec_heading p {
    font-size: 1rem;
    line-height: 22px;
    margin: 20px 0;
  }
}

.categorySec_search .commonSearch {
  margin: 0 auto;
  max-width: 400px;
}

.categorySec_search .commonSearch .form-control {
  width: 100%;
}

.categorySec_fliters {
  padding: 30px 0 50px;
}

@media (width <= 991px) {
  .categorySec_fliters {
    padding: 20px 0 30px;
  }
}

@media (width <= 767px) {
  .categorySec_fliters {
    padding: 20px 0 10px;
  }
}

.categorySec_fliters_inner {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding: 0 1.5rem;
}

@media (width <= 991px) {
  .categorySec_fliters_inner {
    padding: 0 1rem;
  }
}

@media (width <= 767px) {
  .categorySec_fliters_inner {
    margin-bottom: 20px;
  }
}

.categorySec_fliters_inner .slider {
  display: flex;
  overflow-x: auto;
  scrollbar-width: none;
}

.categorySec_fliters_inner .slider::-webkit-scrollbar {
  display: none;
}

.categorySec_fliters_inner .scroll-btn {
  background-color: #00adef;
  color: #fff;
  border: none;
  padding: 0;
  cursor: pointer;
  font-size: 1.2rem;
  min-width: 30px;
  min-height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10rem;
  position: relative;
}

.categorySec_fliters_inner .scroll-btn.left {
  left: -30px;
}

@media (width <= 767px) {
  .categorySec_fliters_inner .scroll-btn.left {
    left: -14px;
  }
}

.categorySec_fliters_inner .scroll-btn.left img {
  transform: rotate(180deg);
}

.categorySec_fliters_inner .scroll-btn.right {
  right: -30px;
}

@media (width <= 767px) {
  .categorySec_fliters_inner .scroll-btn.right {
    right: -14px;
  }
}

.categorySec_fliters_inner .scroll-btn:hover {
  background-color: #00adef;
}

.categorySec_fliters_inner .scroll-btn.disabled, .categorySec_fliters_inner .scroll-btn:disabled {
  background-color: #414c60;
}

.categorySec_fliters_boxbutton {
  width: auto;
  min-height: 35px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.5rem;
  letter-spacing: -.1px;
  background-color: #00adef26;
  border: 0;
  transition: all .3s ease-in-out;
  padding: 5px 10px;
  cursor: pointer;
  color: #fff !important;
}

@media (width <= 767px) {
  .categorySec_fliters_boxbutton {
    font-size: .875rem;
  }
}

.categorySec_fliters_boxbutton .active, .categorySec_fliters_boxbutton a {
  color: #fff !important;
}

.categorySec_fliters_boxbutton:last-child {
  margin-right: 0;
}

.categorySec_fliters_boxbutton:hover, .categorySec_fliters_boxbutton.active, .categorySec_fliters_boxbutton .selected {
  background-color: #00adef;
}

.categorySec_fliters_boxadd {
  padding: 5px 15px;
  background-color: #00adef;
  border-radius: 15px;
  display: inline-flex;
  align-items: center;
}

.categorySec_fliters_boxadd h6 {
  color: #fff;
}

.categorySec_pagination {
  display: flex;
  justify-content: flex-end;
}

.categorySec_term_content p {
  color: #c5c5c5;
  font-size: 1.125rem;
  font-weight: 500;
  line-height: 28px;
  letter-spacing: -.1px;
}


/*# sourceMappingURL=src_css_38bee3c6._.css.map*/
