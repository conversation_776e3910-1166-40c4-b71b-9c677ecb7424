/* [project]/src/css/Home/Category.scss.css [app-client] (css) */
:root {
  --font-g<PERSON>roy: "<PERSON><PERSON>", sans-serif;
}

.categorySec .container {
  max-width: 1080px;
}

.categorySec_heading h1 {
  font-weight: 800;
  font-size: 3rem !important;
}

.categorySec_heading p {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 28px;
  letter-spacing: -.1px;
  margin: 30px 0;
}

@media (width <= 991px) {
  .categorySec_heading p {
    font-size: 1rem;
    line-height: 22px;
    margin: 20px 0;
  }
}

.categorySec_search .commonSearch {
  margin: 0 auto;
  max-width: 400px;
}

.categorySec_search .commonSearch .form-control {
  width: 100%;
}

.categorySec_fliters {
  padding: 30px 0 50px;
}

@media (width <= 991px) {
  .categorySec_fliters {
    padding: 20px 0 30px;
  }
}

@media (width <= 767px) {
  .categorySec_fliters {
    padding: 20px 0 10px;
  }
}

.categorySec_fliters_inner {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding: 0 1.5rem;
}

@media (width <= 991px) {
  .categorySec_fliters_inner {
    padding: 0 1rem;
  }
}

@media (width <= 767px) {
  .categorySec_fliters_inner {
    margin-bottom: 20px;
  }
}

.categorySec_fliters_inner .slider {
  display: flex;
  overflow-x: auto;
  scrollbar-width: none;
}

.categorySec_fliters_inner .slider::-webkit-scrollbar {
  display: none;
}

.categorySec_fliters_inner .scroll-btn {
  background-color: #00adef;
  color: #fff;
  border: none;
  padding: 0;
  cursor: pointer;
  font-size: 1.2rem;
  min-width: 30px;
  min-height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10rem;
  position: relative;
}

.categorySec_fliters_inner .scroll-btn.left {
  left: -30px;
}

@media (width <= 767px) {
  .categorySec_fliters_inner .scroll-btn.left {
    left: -14px;
  }
}

.categorySec_fliters_inner .scroll-btn.left img {
  transform: rotate(180deg);
}

.categorySec_fliters_inner .scroll-btn.right {
  right: -30px;
}

@media (width <= 767px) {
  .categorySec_fliters_inner .scroll-btn.right {
    right: -14px;
  }
}

.categorySec_fliters_inner .scroll-btn:hover {
  background-color: #00adef;
}

.categorySec_fliters_inner .scroll-btn.disabled, .categorySec_fliters_inner .scroll-btn:disabled {
  background-color: #414c60;
}

.categorySec_fliters_boxbutton {
  width: auto;
  min-height: 35px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.5rem;
  letter-spacing: -.1px;
  background-color: #00adef26;
  border: 0;
  transition: all .3s ease-in-out;
  padding: 5px 10px;
  cursor: pointer;
  color: #fff !important;
}

@media (width <= 767px) {
  .categorySec_fliters_boxbutton {
    font-size: .875rem;
  }
}

.categorySec_fliters_boxbutton .active, .categorySec_fliters_boxbutton a {
  color: #fff !important;
}

.categorySec_fliters_boxbutton:last-child {
  margin-right: 0;
}

.categorySec_fliters_boxbutton:hover, .categorySec_fliters_boxbutton.active, .categorySec_fliters_boxbutton .selected {
  background-color: #00adef;
}

.categorySec_fliters_boxadd {
  padding: 5px 15px;
  background-color: #00adef;
  border-radius: 15px;
  display: inline-flex;
  align-items: center;
}

.categorySec_fliters_boxadd h6 {
  color: #fff;
}

.categorySec_pagination {
  display: flex;
  justify-content: flex-end;
}

.categorySec_term_content p {
  color: #c5c5c5;
  font-size: 1.125rem;
  font-weight: 500;
  line-height: 28px;
  letter-spacing: -.1px;
}

/*# sourceMappingURL=src_css_Home_Category_scss_b52d8e88.css.map*/