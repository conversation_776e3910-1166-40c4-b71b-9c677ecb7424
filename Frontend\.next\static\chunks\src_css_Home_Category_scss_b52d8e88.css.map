{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/css/Home/Category.scss.css"], "sourcesContent": [":root{--font-g<PERSON>roy: \"<PERSON><PERSON>\", sans-serif}.categorySec .container{max-width:1080px}.categorySec_heading h1{font-size:3rem !important;font-weight:800}.categorySec_heading p{font-size:1.25rem;font-weight:600;line-height:28px;letter-spacing:-0.1px;margin:30px 0}@media(max-width: 991px){.categorySec_heading p{font-size:1rem;line-height:22px;margin:20px 0}}.categorySec_search .commonSearch{margin:0 auto;max-width:400px}.categorySec_search .commonSearch .form-control{width:100%}.categorySec_fliters{padding:30px 0 50px}@media(max-width: 991px){.categorySec_fliters{padding:20px 0 30px}}@media(max-width: 767px){.categorySec_fliters{padding:20px 0 10px}}.categorySec_fliters_inner{display:flex;align-items:center;margin-bottom:30px;padding:0 1.5rem}@media(max-width: 991px){.categorySec_fliters_inner{padding:0 1rem}}@media(max-width: 767px){.categorySec_fliters_inner{margin-bottom:20px}}.categorySec_fliters_inner .slider{display:flex;overflow-x:auto;scrollbar-width:none}.categorySec_fliters_inner .slider::-webkit-scrollbar{display:none}.categorySec_fliters_inner .scroll-btn{background-color:#00adef;color:#fff;border:none;padding:0;cursor:pointer;font-size:1.2rem;min-width:30px;min-height:30px;display:flex;align-items:center;justify-content:center;border-radius:10rem;position:relative}.categorySec_fliters_inner .scroll-btn.left{left:-30px}@media(max-width: 767px){.categorySec_fliters_inner .scroll-btn.left{left:-14px}}.categorySec_fliters_inner .scroll-btn.left img{transform:rotate(180deg)}.categorySec_fliters_inner .scroll-btn.right{right:-30px}@media(max-width: 767px){.categorySec_fliters_inner .scroll-btn.right{right:-14px}}.categorySec_fliters_inner .scroll-btn:hover{background-color:#00adef}.categorySec_fliters_inner .scroll-btn.disabled,.categorySec_fliters_inner .scroll-btn:disabled{background-color:#414c60}.categorySec_fliters_boxbutton{width:auto;min-height:35px;border-radius:5px;display:flex;align-items:center;justify-content:center;margin-right:10px;color:#fff !important;font-size:1rem;font-weight:600;line-height:1.5rem;letter-spacing:-0.1px;background-color:rgba(0,173,239,.15);border:0;transition:all ease-in-out .3s;padding:5px 10px;cursor:pointer}@media(max-width: 767px){.categorySec_fliters_boxbutton{font-size:.875rem}}.categorySec_fliters_boxbutton .active,.categorySec_fliters_boxbutton a{color:#fff !important}.categorySec_fliters_boxbutton:last-child{margin-right:0}.categorySec_fliters_boxbutton:hover,.categorySec_fliters_boxbutton.active,.categorySec_fliters_boxbutton .selected{background-color:#00adef}.categorySec_fliters_boxadd{padding:5px 15px;background-color:#00adef;border-radius:15px;display:inline-flex;align-items:center}.categorySec_fliters_boxadd h6{color:#fff}.categorySec_pagination{display:flex;justify-content:flex-end}.categorySec_term_content p{color:#c5c5c5;font-size:1.125rem;font-weight:500;line-height:28px;letter-spacing:-0.1px}"], "names": [], "mappings": "AAAA;;;;AAA0C;;;;AAAyC;;;;;AAAkE;;;;;;;;AAA8G;EAAyB;;;;;;;AAAsE;;;;;AAAgE;;;;AAA2D;;;;AAAyC;EAAyB;;;;;AAA0C;EAAyB;;;;;AAA0C;;;;;;;AAA+F;EAAyB;;;;;AAA2C;EAAyB;;;;;AAA+C;;;;;;AAAqF;;;;AAAmE;;;;;;;;;;;;;;;;AAA6P;;;;AAAuD;EAAyB;;;;;AAAwD;;;;AAAyE;;;;AAAyD;EAAyB;;;;;AAA0D;;;;AAAsE;;;;AAAyH;;;;;;;;;;;;;;;;;;;;AAAgW;EAAyB;;;;;AAAkD;;;;AAA8F;;;;AAAyD;;;;AAA6I;;;;;;;;AAAgI;;;;AAA0C;;;;;AAA8D"}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}