<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
//use App\Http\Services\AuthService;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Auth\RegisterRequest;
use App\Models\Plan;
use App\Models\User;
use App\Rules\ValidUsername;
use App\Rules\NoProfanity;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use SendGrid\Mail\Mail;
use App\Models\UserPassword;
use App\Models\UserSubscription;
use App\Models\LoginActivity;

/**
 * @group Authentication
 *
 * APIs for user authentication, registration, verification, and password management.
 */

class AuthController extends Controller
{
    use ApiResponseTrait;

    // protected $authServices;
    protected $user;
    protected $verificationTemplateId;
    protected $forgetPasswordTemplateId;

    protected $lockoutResetPasswordMessage;
    protected $lockoutMessageDelayTime;
    protected $lockoutTemplateId_1;
    protected $lockoutTemplateId_2;
    protected $lockoutTemplateId_3;
    protected $sendGridAPIKEY;

    public function __construct()
    {
        // $this->authServices = $authServices;
        $this->user = new User();
        $this->sendGridAPIKEY = env('SENDGRID_API_KEY');
        $this->forgetPasswordTemplateId = config('mail.templates.forget_password');
        $this->verificationTemplateId = config('mail.templates.verification_token');
        $this->lockoutTemplateId_1 = config('mail.templates.lockout_template_id_1');
        $this->lockoutTemplateId_2 = config('mail.templates.lockout_template_id_2');
        $this->lockoutTemplateId_3 = config('mail.templates.lockout_template_id_3');
        $this->lockoutResetPasswordMessage = 'We’ve restricted access to your account due to multiple failed login attempts. Please verify your identity below to reset your password and unlock your account.';
        $this->lockoutMessageDelayTime = 'Your account is temporarily locked due to multiple failed login attempts. Please wait 15 minutes before trying again.';
    }



    /**
     * Register new user
     *
     * @bodyParam email string required Email address
     * @bodyParam password string required Password
     * @bodyParam uuid string required Temporary UUID for registration session
     *
     * @response 200 {"success": true, "message": "A verification email has been sent.", "data": []}
     */
    public function register(RegisterRequest $request)
    {
        try {
            $token = $this->generateToken();
            $tokenExpiresAt = now()->addMinutes(15);
            $cacheKey = "signup_data_{$request->uuid}";

            $existingCache = Cache::get($cacheKey) ?? [];

            $tokens = $existingCache['tokens'] ?? [];
            $tokens[] = [
                'token' => $token,
                'expires_at' => $tokenExpiresAt,
                'verified' => false,
            ];

            $tokens = $this->removeExpiredTokens($tokens);

            $cacheData = array_merge($existingCache, [
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'tokens' => array_values($tokens),
                'pricing' => $request->pricing,
                'is_trial' => $request->trial ?? false,
            ]);

            Cache::put($cacheKey, $cacheData, $tokenExpiresAt);

            $this->sendVerificationEmail($request->email, $token, $this->verificationTemplateId);

            return $this->successResponse('A verification email has been sent.', 200);
        } catch (\Exception $e) {
            return $this->errorResponse('Verification Email Sending Failed', 400);
        }
    }


    /**
     * Resend verification code
     *
     * @bodyParam type string required Must be either signup_data or reset_password_data
     * @bodyParam uuid string required The UUID used during signup or password reset
     *
     * @response 200 {"success": true, "message": "A new verification email has been sent.", "data": []}
     */
    public function resendVerificationCode(Request $request)
    {
        try {
            $request->validate([
                'type' => 'required|in:signup_data,reset_password_data,signup_google,signup_facebook',
                'uuid' => 'required|string|uuid',
            ]);

            $cacheKey = "{$request->type}_{$request->uuid}";
            $cacheData = Cache::get($cacheKey) ?? [];

            $email = $cacheData['email'] ?? null;

            $rateLimitKey = "resend_lock_{$request->type}_{$request->uuid}";
            if (Cache::has($rateLimitKey)) {
                return $this->errorResponse('Please wait a moment before requesting another verification code.', 429);
            }
            Cache::put($rateLimitKey, true, now()->addSeconds(60));

             $token = $this->generateToken();
//            $token = 123456;
            $tokenExpiresAt = now()->addMinutes(15);


            $existingTokens = $cacheData['tokens'] ?? [];
            $existingTokens[] = [
                'token' => $token,
                'expires_at' => $tokenExpiresAt,
                'verified' => false
            ];


            $existingTokens = $this->removeExpiredTokens($existingTokens);

            Cache::put($cacheKey, array_merge($cacheData, [
                'email' => $email,
                'tokens' => $existingTokens
            ]), $tokenExpiresAt);


            $templateId = $request->type === 'reset_password_data'
                ? $this->forgetPasswordTemplateId
                : $this->verificationTemplateId;

            $type = $request->type === 'reset_password_data' ? 'forgot_password' : 'signup';

            $this->sendVerificationEmail($email, $token, $templateId, null, $type);

            return $this->successResponse(
                'Verification code resent! Please check your email (including spam or junk folders) for the new code. If you don’t receive it within a few minutes, try resending or contact our support team for assistance.',
                200
            );

        } catch (\Throwable $e) {
            Log::error('Resend Verification Error: ' . $e->getMessage());
            return $this->errorResponse('Failed to send verification email. Please try again.', 500);
        }
    }



    /**
     * Verify token
     *
     * @bodyParam token string required 6-digit token
     * @bodyParam type string required Must be either signup_data or reset_password_data
     * @bodyParam uuid string required UUID used during the signup/reset process
     *
     * @response 200 {"success": true, "message": "Verification Code verified successfully. Proceed to username creation.", "data": []}
     */
    public function verifyToken(Request $request)
    {
        try {
            $request->validate([
                'token' => 'required|string|size:6',
                'type' => 'required|in:signup_data,reset_password_data,signup_google,signup_facebook',
                'uuid' => 'required|string|uuid',
            ]);

            $cacheKey = "{$request->type}_{$request->uuid}";

            $cacheData = Cache::get($cacheKey);

            if (!$cacheData || !isset($cacheData['tokens']) || !is_array($cacheData['tokens'])) {
                return $this->errorResponse('Invalid or expired code. Please try again.', 400);
            }

            $found = false;
            foreach ($cacheData['tokens'] as &$tokenData) {
                if (
                    $tokenData['token'] === $request->token &&
                    now()->lessThan($tokenData['expires_at']) &&
                    !$tokenData['verified']
                ) {
                    $tokenData['verified'] = true;
                    $cacheData['verified'] = true;
                    $cacheData['verified_at'] = now();
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                return $this->errorResponse('Invalid or expired code. Please try again.', 400);
            }

            // Save updated cache
            Cache::put($cacheKey, $cacheData, now()->addMinutes(15));

            return $this->successResponse([], 'Verification Code verified successfully. Proceed to username creation.', 200);

        } catch (\Exception $e) {
            return $this->errorResponse('Verification Code verification Failed', 400);
        }
    }




    /**
     * Create or check username
     *
     * @bodyParam username string The desired username
     * @bodyParam uuid string required UUID from signup
     * @bodyParam flag boolean Whether to only check availability
     *
     * @response 201 {"success": true, "message": "Account created successfully", "data": []}
     */
    public function createUsername(Request $request)
    {
        try {
            $checkUniqueness = filter_var($request->flag, FILTER_VALIDATE_BOOLEAN);

            $rules = [
                'uuid' => 'required|string|uuid',
                'type' => 'required|string|in:signup_data,signup_facebook,signup_google',
                'username' => [
                    'string',
                    'max:20',
                    new ValidUsername(),
                    new NoProfanity(),
                ],
            ];

            if ($checkUniqueness) {
                $rules['username'][] = 'required';
                $rules['username'][] = 'unique:users,username';
            } else {
                $rules['username'][] = 'nullable';
            }

            $messages = [
                'username.unique' => 'This username is already taken.',
            ];

            $validator = Validator::make($request->all(), $rules, $messages);

            if ($validator->fails()) {
                $errors = $validator->errors();
                $usernameInput = strtolower(preg_replace('/[^a-z0-9]/', '', $request->username));
                $suggestions = [];

                if ($errors->has('username') && $errors->first('username') === 'This username is already taken.') {
                    $suggestions = $this->generateUsernameSuggestions($usernameInput);
                }

                return $this->errorResponse(
                    $errors->first('username'),
                    400,
                    ['suggestions' => $suggestions]
                );
            }

            if ($checkUniqueness) {
                return $this->successResponse([], 'Username is available.', 200);
            }

            $cacheKey = $request->type . '_' . $request->uuid;
            $cacheData = Cache::get($cacheKey);

            if (!$cacheData || empty($cacheData['verified']) || !$cacheData['verified']) {
                return $this->errorResponse(
                    'Token verification has expired.',
                    401,
                    [],
                    ['type' => 'token_expired', 'state' => true]
                );
            }

            $pricing = $cacheData['pricing'] ?? 'free';
            $isTrial = (bool) ($cacheData['is_trial'] ?? false);

            $planIdMap = [
                'free' => 1,
                'pro' => 2,
                'premium' => 3,
                'essential' => 4,
            ];

            $planId = $planIdMap[$pricing] ?? 1;

            $userData = [
                'email' => $cacheData['email'],
                'password' => $cacheData['password'],
                'username' => $request->username,
                'email_verified_at' => $cacheData['verified_at'],
                'avatar' => $cacheData['avatar'] ?? null,
                'subscription_id' => $planId,
            ];

            if (!empty($cacheData['google_id'])) {
                $userData['google_id'] = $cacheData['google_id'];
            }
            if (!empty($cacheData['facebook_id'])) {
                $userData['facebook_id'] = $cacheData['facebook_id'];
            }

            $token = '';
            $userResponseData = '';

            DB::transaction(function () use (&$token, &$userResponseData, $userData, $planId, $isTrial) {
                $user = User::create($userData);

                UserPassword::create([
                    'user_id' => $user->id,
                    'password' => $userData['password'],
                ]);

                $freePlan = Plan::where('billing_type', 'free')->first();

                UserSubscription::create([
                    'user_id'                  => $user->id,
                    'plan_id'                  => $freePlan->id,
                    'stripe_subscription_id'   => null,
                    'is_trial'                 => false,
                    'status'                   => 'active',
                    'starts_at'                => null,
                    'ends_at'                  => null,
                ]);

                $user->load('activeSubscription');

                $token = $user->createToken('API Token')->plainTextToken;
                $userResponseData = $user->only(['id', 'email', 'name', 'username', 'role','subscription_id']);
            });

            Cache::forget($cacheKey);

            return $this->successResponse(
                [
                    'user' => $userResponseData,
                    'plan' => $planId == 1 ? 'free' : null, // ✅ return "free" if plan ID is 1
                    'token' => $token
                ],
                'Account created successfully'
            );


            //                return $this->successResponse('Account created successfully.', 201);

        } catch (\Exception $e) {
            Log::error('User creation failed: ' . $e->getMessage());
            return $this->errorResponse('Failed to create user. Please try again.', 400);
        }
    }



    private function generateToken()
    {
        $characters = 'ACDEFGHJKMNPQRTUVWXYZ234679';
        return substr(str_shuffle(str_repeat($characters, 6)), 0, 6);
    }


    /**
     * Request password reset
     *
     * @bodyParam type string required email or username
     * @bodyParam value string required The actual email or username
     * @bodyParam uuid string required A unique identifier for this reset flow
     *
     * @response 201 {"success": true, "message": "Verification email sent successfully", "data": {"token_expires_at": "..."}}
     */
    public function forgetPassword(Request $request)
    {
        try {
            $request->validate([
                'type' => 'required|string|in:email,username',
                'value' => 'required|string|max:64',
                'uuid' => 'required|string|uuid',
            ]);

            $user = $request->type === 'email'
                ? User::where('email', $request->value)->first()
                : User::where('username', $request->value)->first();

            if ($user) {
                $token = $this->generateToken();
                $tokenExpiresAt = now()->addMinutes(15);
                $cacheKey = "reset_password_data_{$request->uuid}";

                $existingCache = Cache::get($cacheKey) ?? [];

                $tokens = $existingCache['tokens'] ?? [];
                $tokens[] = [
                    'token' => $token,
                    'expires_at' => $tokenExpiresAt,
                    'verified' => false,
                ];


                $tokens = $this->removeExpiredTokens($tokens);

                Cache::put($cacheKey, array_merge($existingCache, [
                    'email' => $user->email,
                    'tokens' => array_values($tokens),
                ]), $tokenExpiresAt);

                Log::info('Forget password Cache Data ' . print_r(Cache::get($cacheKey), true));

                $this->sendVerificationEmail(
                    $user->email,
                    $token,
                    $this->forgetPasswordTemplateId,
                    $user->username,
                    'forgot_password'
                );
            }

            return $this->successResponse(
                [],
                'If an account with the entered email or username exists, a verification code has been sent.',
                200
            );

        } catch (\Exception $e) {
            Log::error('Forget password request failed: ' . $e->getMessage());
            return $this->errorResponse('Failed to process request. Please try again.', 400);
        }
    }


    /**
     * Reset password
     *
     * @bodyParam uuid string required UUID used during the reset flow
     * @bodyParam new_password string required The new password
     *
     * @response 200 {"success": true, "message": "Password successfully updated.", "data": []}
     */

    public function resetPassword(Request $request)
    {
        try {
            $request->validate([
                'uuid' => 'required|string|uuid',
                'new_password' => 'required|min:8',
            ]);

            $cacheKey = "reset_password_data_{$request->uuid}";
            $cacheData = Cache::get($cacheKey);

            if (!$cacheData || !isset($cacheData['email'])) {
                return $this->errorResponse('Invalid or expired reset request.', 400);
            }

            $user = User::where('email', $cacheData['email'])->first();

            if (!$user) {
                return $this->errorResponse('User not found.', 404);
            }


            $recentHashes = UserPassword::where('user_id', $user->id)
                ->latest()
                ->take(5)
                ->pluck('password');

            foreach ($recentHashes as $oldHash) {
                if (Hash::check($request->new_password, $oldHash)) {
                    return $this->errorResponse('You’ve used this password recently. Please choose a new one you haven’t used before.', 422);
                }
            }

            DB::transaction(function () use ($user, $request) {
                $hashed = Hash::make($request->new_password);

                $user->update([
                    'password' => $hashed,
                    'login_attempts' => 0,
                    'lockout_cycles' => 0,
                    'lockout_until' => null,
                ]);


                $passwordCount = UserPassword::where('user_id', $user->id)->count();

                if ($passwordCount >= 5) {
                    UserPassword::where('user_id', $user->id)
                        ->orderBy('created_at', 'asc')
                        ->limit(1)
                        ->delete();
                }

                UserPassword::create([
                    'user_id' => $user->id,
                    'password' => $hashed,
                ]);

            });

            Cache::forget($cacheKey);

            return $this->successResponse([], 'Password successfully updated.', 200);

        } catch (\Throwable $e) {
            Log::error('Reset Password Error: ' . $e->getMessage());
            return $this->errorResponse('Something went wrong. Please try again.', 500);
        }
    }


    private function sendVerificationEmail($email, $token, $template_id, $username = null, $type = 'signup')
    {
        $sendgrid = new \SendGrid($this->sendGridAPIKEY, ['verify_ssl' => false]);

        $message = new Mail();
        $message->setFrom('<EMAIL>', 'TradeReply');
        $message->setReplyTo('<EMAIL>', 'Support Team');
        $message->setSubject("Verify Your Account");
        $message->addTo($email);
        $message->setTemplateId($template_id);

        if (in_array($type, ['signup', 'forgot_password']) && $token) {
            $message->addDynamicTemplateData('accountVerificationToken', $token);
        }

        if (in_array($type, ['forgot_password', 'lockout'])) {
            $message->addDynamicTemplateData('accountUsername', $username);
            $message->addDynamicTemplateData('accountEmail', $email);
        }

        try {
            $response = $sendgrid->send($message);
            if ($response->statusCode() >= 200 && $response->statusCode() < 300) {
                return true;
            }
        } catch (\Exception $e) {
            \Log::error("SendGrid Email Error: " . $e->getMessage());
        }

        return false;
    }



    /**
     * Login existing user
     *
     * @bodyParam email string required Email
     * @bodyParam password string required Password
     *
     * @response 200 {"success": true, "message": "API login successful", "data": {"user": {...}}}
     */
    public function login(LoginRequest $request)
    {
        try {
            $user = User::with(['activeSubscription'])->where('email', $request->email)->first();

            if (!$user) {
                return $this->errorResponse('Incorrect email or password.', 401, [], ['type' => 'captcha', 'state' => false]);
            }

            if ($user->lockout_cycles >= 3) {

                return $this->errorResponse(
                    $this->lockoutResetPasswordMessage,
                    423,
                    [],
                    ['type' => 'redirect', 'state' => true,'redirect_to' => '/locate-account']
                );
            }

            if ($user->lockout_until && now()->lessThan($user->lockout_until)) {
                $remaining = now()->diffInMinutes($user->lockout_until, false);
                $remaining = max(1, $remaining);
                $message = "Your account is temporarily locked due to multiple failed login attempts. Please wait $remaining minute" . ($remaining > 1 ? 's' : '') . " before trying again.";

                return $this->errorResponse(
                    $message,
                    423,
                    [],
                    //                    ['type' => 'lockout_wait', 'state' => true, 'until' => $user->lockout_until]
                );
            }


            $captchaRequired = $this->shouldShowCaptcha($request->email);

            if (!Hash::check($request->password, $user->password)) {
                if ($captchaRequired) {
                    if (!$request->filled('captchaToken') || !$this->verifyRecaptcha($request->captchaToken)) {
                        return $this->errorResponse(
                            'reCAPTCHA verification failed. Please try again.',
                            403,
                            [],
                            ['type' => 'captcha', 'state' => true]
                        );
                    }
                }

                $error = $this->handleFailedAttempt($user);

                if (is_array($error) && isset($error['type']) && $error['type'] === 'redirect') {
                    return $this->errorResponse($error['message'], 423, [], [
                        'type' => $error['type'],
                        'state' => $error['state'],
                        'redirect_to' => $error['redirect_to']
                    ]);
                }

                return $this->errorResponse($error, 401, [], ['type' => 'captcha', 'state' => $captchaRequired]);
            }


            $user->update([
                'login_attempts' => 0,
                'lockout_cycles' => 0,
                'lockout_until' => null
            ]);

            $token = $user->createToken('API Token')->plainTextToken;
            $userData = $user->only(['id', 'email', 'name', 'username', 'role','subscription_id']);

            // Log login activity
            try {
                LoginActivity::createFromRequest($request, $user, $token);
            } catch (\Exception $e) {
                Log::warning('Failed to log login activity: ' . $e->getMessage());
            }

            return $this->successResponse(
                [
                    'user' => $userData,
                    'active_subscription' => $user->activeSubscription,
                    'captcha_required' => false,
                    'token' => $token // add the token here
                ],
                'API login successful'
            );



        } catch (\Exception $e) {
            Log::error('Login error: ' . $e->getMessage(), [
                'email' => $request->email,
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse('An unexpected error occurred. Please try again later.', 500);
        }
    }



    /*
     * Decide whether to show CAPTCHA
    */
    protected function shouldShowCaptcha($email)
    {
        $user = User::where('email', $email)->first();

        return $user &&
            (
                $user->login_attempts >= 3 || $user->lockout_cycles > 0
            ) &&
            (
                !$user->lockout_until || now()->greaterThan($user->lockout_until)
            );
    }



    /*
     * Handle failed login attempts
     */
    protected function handleFailedAttempt($user)
    {
        if (!$user) {
            return 'Incorrect email or password.';
        }

        if ($user->lockout_cycles >= 3) {
            return $this->lockoutResetPasswordMessage;
        }

        if ($user->lockout_until && now()->lessThan($user->lockout_until)) {
            $remaining = now()->diffInMinutes($user->lockout_until, false);
            $remaining = max(1, $remaining);
            return  "Your account is temporarily locked due to multiple failed login attempts. Please wait $remaining minute" . ($remaining > 1 ? 's' : '') . " before trying again.";
        }

        if ($user->lockout_cycles === 2 && $user->login_attempts + 1 >= 5) {
            $user->lockout_until = null;
            $user->save();
        }

        $user->increment('login_attempts');

        if ($user->login_attempts > 4) {
            if ($user->lockout_cycles === 2) {
                $user->update([
                    'lockout_until' => null,
                    'lockout_cycles' => 3,
                    'login_attempts' => 0,
                ]);

                $this->sendLockoutEmail($user, 3);
                return [
                    'message' => $this->lockoutResetPasswordMessage,
                    'type' => 'redirect',
                    'state' => true,
                    'redirect_to' => '/locate-account'
                ];
            }

            $lockoutDuration = now()->addMinutes(15);
            $newCycle = $user->lockout_cycles + 1;

            $user->update([
                'lockout_until' => $lockoutDuration,
                'lockout_cycles' => $newCycle,
                'login_attempts' => 0,
            ]);

            $this->sendLockoutEmail($user, $newCycle);

            return "Your account is temporarily locked due to multiple failed login attempts. Please wait 15 minutes before trying again.";
        }

        return 'Incorrect email or password.';
    }



    protected function sendLockoutEmail($user, $cycle)
    {
        $templateMap = [
            1 => $this->lockoutTemplateId_1,
            2 => $this->lockoutTemplateId_2,
            3 => $this->lockoutTemplateId_3,
        ];

        $templateId = $templateMap[$cycle] ?? null;

        if ($templateId) {
            $this->sendVerificationEmail(
                $user->email,
                null,
                $templateId,
                $user->username,
                'lockout'
            );

        }
    }




    protected function verifyRecaptcha($token)
    {
        try {
            Log::info('Starting reCAPTCHA verification.', [
                'token' => $token,
                'site_key' => env('RECAPTCHA_SITE_KEY'),
            ]);

            $response = Http::asForm()
                ->withoutVerifying()
                ->post('https://www.google.com/recaptcha/api/siteverify', [
                    'secret' => env('RECAPTCHA_SECRET_KEY'),
                    'response' => $token,
                ]);

            $json = $response->json();
            Log::info('reCAPTCHA API response', ['response' => $json]);

            $score = $json['score'] ?? null;

            // For v3 (with score), ensure it's a bot check
            if (isset($json['success']) && $json['success']) {
                if ($score !== null) {
                    Log::info('reCAPTCHA score', ['score' => $score]);
                    return $score >= 0.5;
                }

                // If no score exists, it's likely reCAPTCHA v2
                return true;
            }

            Log::warning('reCAPTCHA failed or invalid response', ['json' => $json]);
            return false;
        } catch (\Exception $e) {
            Log::error('reCAPTCHA verification failed', ['error' => $e->getMessage()]);
            return false;
        }
    }




    private function removeExpiredTokens(array $tokens): array
    {
        return array_values(array_filter($tokens, function ($t) {
            return isset($t['expires_at']) && now()->lessThan($t['expires_at']);
        }));
    }


    /**
     * Logout user
     *
     * Requires authentication via bearer token or cookie
     *
     * @response 200 {"message": "Logged out successfully"}
     */

    public function logout(Request $request)
    {
        $user = $request->user();

        if ($user) {
            $token = $request->bearerToken();

            // Mark login activity as logged out
            if ($token) {
                LoginActivity::where('user_id', $user->id)
                    ->where('session_token', $token)
                    ->whereNull('logged_out_at')
                    ->update(['logged_out_at' => now()]);
            }

            $request->user()->currentAccessToken()->delete();
        }

        return response()->json(['message' => 'Logged out successfully'])
            ->withCookie(
                cookie()->forget('auth_token')
            );
    }


    private function generateUsernameSuggestions($base, $count = 4)
    {
        $suggestions = [];
        $base = substr($base, 0, 15);

        for ($i = 0; $i < $count * 2; $i++) {
            $candidate = $base . rand(1, 9999);

            if (!User::whereRaw('LOWER(username) = ?', [strtolower($candidate)])->exists()) {
                $suggestions[] = $candidate;
            }

            if (count($suggestions) >= $count) {
                break;
            }
        }

        return $suggestions;
    }


    public function deleteAccount(Request $request)
    {
        $id = $request->input('id');

        $user = \App\Models\User::find($id);

        if (!$user) {
            return response()->json(['message' => 'User not found'], 404);
        }

        $user->delete();

        return response()->json(['message' => 'User account deleted successfully']);
    }



    public function handleFacebookDeauthorize(Request $request)
    {
        $signedRequest = $request->input('signed_request');

        // You need to parse this signed request and extract the user ID
        $payload = $this->parseFacebookSignedRequest($signedRequest);

        if (!$payload || !isset($payload['user_id'])) {
            return response()->json(['message' => 'Invalid request'], 400);
        }

        // Optional: Log for tracking
        \Log::info('Facebook deauthorization request:', $payload);

        // Delete the user or mark them as deauthorized
        $user = User::where('facebook_id', $payload['user_id'])->first();
        if ($user) {
            $user->delete(); // or deactivate
        }

        return response()->json(['success' => true]);
    }

    private function parseFacebookSignedRequest($signedRequest)
    {
        $secret = env('FACEBOOK_APP_SECRET');
        [$encodedSig, $payload] = explode('.', $signedRequest, 2);

        $sig = base64_decode(strtr($encodedSig, '-_', '+/'));
        $data = json_decode(base64_decode(strtr($payload, '-_', '+/')), true);

        $expectedSig = hash_hmac('sha256', $payload, $secret, true);

        return hash_equals($sig, $expectedSig) ? $data : null;
    }


}
