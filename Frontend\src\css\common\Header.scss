@use "../theme/var" as *;

header {
  position: sticky;
  top: 0;
  left: 0;
  z-index: 9998;
}

.home-page {
  .siteHeader {
    background-color: $black !important;
    border-bottom: 0;

    .navMenu {
      .common_dropdown {
        &.dropdown {
          .dropdown-toggle {

            &.show,
            &:hover {
              @media (min-width: 1200px) {
                background-color: $clr2A2E39 !important;
                color: $white;

                &::after {
                  transform: rotate(180deg);
                }
              }
            }
          }

          &.show {
            .dropdown-toggle {
              &::after {
                transform: rotate(180deg);
              }
            }
          }
        }

        .dropdown-menu {
          @media (min-width: 1200px) {
            background-color: $clr1E222D !important;
          }
        }
      }
    }

    .common_dropdown {
      &.dropdown {
        .dropdown-menu {
          .dropdown-item {

            &:hover,
            &.active,
            &:focus {
              background-color: $clr2A2E39 !important;
            }

            &.white_stroke_icon {
              font-weight: 700;
              background: $gradientblackbg !important;
              color: $clr00b9ff;
              transition: none;

              svg {
                path {
                  fill: $clr00b9ff;
                }
              }

              &:hover,
              &.active {
                background: $clr2A2E39 !important;
              }
            }
          }
        }
      }
    }

    .navbar {
      &-collapse {
        .nav-link {
          @media (max-width: 1199px) {
            &.white_stroke_icon {
              font-weight: 700;
              background: linear-gradient(to right,
                  #000000,
                  #2d2d2d) !important;
              color: #00aeef;
              transition: none;

              svg {
                path {
                  fill: $clr00b9ff;
                }
              }
            }
          }
        }
      }
    }

    .navbar-collapse {
      @media (max-width: 1199px) {
        background-color: rgba(0, 0, 0, 0.9) !important;
      }
    }
  }

  @media (width >=1200px) {

    .siteHeader .navbar-collapse .nav-link:hover,
    .siteHeader .navbar-collapse .nav-link.active,
    .siteHeader .navbar-collapse .nav-link:focus {
      background-color: #2a2e39 !important;
      color: #fff;
    }

    .languageDropdown {
      width: 64px;

      @media (max-width: 1199px) {
        width: 100%;
      }

      .common_dropdown {
        @media (max-width: 1199px) {
          width: 100%;
        }

        .nav-link:hover,
        .nav-link.active,
        .nav-link:focus {
          color: #fff;
          background-color: $clr2A2E39 !important;
        }
      }
    }

    .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon:hover,
    .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon.active {
      background: #2a2e39 !important;
    }
  }
}

.siteHeader {
  height: 80px;
  padding: 1rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  background-color: $clr031940;
  border-bottom: 1px solid $clr064197;

  .btn-style {
    min-height: 56px;
    min-width: 169px;

    @media (max-width: 1199px) {
      min-height: 40px;
      min-width: 120px;
      padding: 8px 1rem;
      font-size: 14px;
    }

    @media (max-width: 575px) {
      min-height: 34px;
      min-width: 80px;
      font-size: 14px;
      ;
    }
  }

  @media (max-width: 1199px) {
    z-index: 9999;
    backdrop-filter: none;
  }

  @media (max-width: 767px) {
    padding: 0.625rem 0;
  }

  .navbar {
    padding: 0;
    width: 100%;

    .brandLogo {
      img {
        max-width: 190px;
        width: 100%;

        @media (max-width: 767px) {
          max-width: 150px;
          margin-right: 0rem;
        }

        @media (max-width: 360px) {
          max-width: 120px;
          margin-right: 0rem;
        }
      }
    }

    &-collapse {
      height: auto !important;

      .nav-link {
        font-size: 1.25rem;
        font-weight: 400;
        background-color: transparent;
        display: flex;
        align-items: center;
        white-space: nowrap;
        padding: 0.5rem 1.5rem;
        color: $white;

        &:hover,
        &.active,
        &:focus {
          color: $baseclr;

          @media (min-width: 1200px) {
            background-color: $clr283f67 !important;
            color: $white;
          }
        }

        @media (min-width: 1200px) {
          margin: 0 3px;
        }

        @media (max-width: 1199px) {
          padding: 1.25rem 0rem;
          border-bottom: 1px solid rgba(255, 255, 255, 0.2);
          font-size: 1.125rem;

          img {
            width: 22px;
          }

          &.white_stroke_icon {
            font-weight: 700;
            background: linear-gradient(to right, #031940, #283f67);
            color: #00aeef;
            transition: none;

            svg {
              path {
                fill: $clr00b9ff;
              }
            }

            // &:hover,
            // &.active {
            //     background: $clr283f67;
            // }
          }
        }
      }

      @media (max-width: 1199px) {
        position: fixed;
        left: -350px;
        top: 0px;
        background-color: rgba(3, 25, 64, 0.9);
        backdrop-filter: blur(5px);
        width: 350px;
        padding: 1.25rem 1rem;
        display: block;
        transition: all ease-in-out 0.2s;
        height: 100vh !important;
        z-index: 9999;
        padding: 0;

        a {
          display: flex;
          justify-content: flex-start;
          text-align: left;
        }

        &.show {
          left: 0;
          height: 100vh;
        }

        .navMenu {
          padding: 20px;
          height: calc(100vh - 90px);
          overflow-y: auto;
        }

        // .languageMenu {
        //     padding: 0px 1.25rem;
        // }
      }

      @media (max-width: 767px) {
        left: -100%;
        width: 100%;
      }

      .navMenu {
        .common_dropdown {
          &.dropdown {
            .dropdown-toggle {
              padding: 0.5rem 1.5rem !important;
              border-radius: 0;

              @media (max-width: 1199px) {
                padding: 1.25rem 0rem !important;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                width: 100%;
              }

              &::after {
                display: block;
                background-image: url("https://cdn.tradereply.com/dev/site-assets/icons/tradereply-drop-arrow.svg");
                background-repeat: no-repeat;
                background-size: 1.15rem;
                background-position: center;
                width: 1.15rem;
                height: 1.15rem;
                border: 0;
                transition: all ease-in-out 0.3s;
                margin-left: 1rem;

                @media (max-width: 1199px) {
                  margin-left: 0;
                  position: absolute;
                  right: 0;
                }
              }

              &.show,
              &:hover {
                @media (min-width: 1200px) {
                  background-color: $clr283f67;
                  color: $white;

                  &::after {
                    transform: rotate(180deg);
                  }
                }
              }
            }

            &.show {
              .dropdown-toggle {
                &::after {
                  transform: rotate(180deg);
                }
              }
            }

            .dropdown-menu {
              @media screen and (max-width: 1199px) {
                position: static;
                border: 0;
                background-color: transparent;
                padding: 0;
              }

              .nav-link {
                padding: 0.875rem 1.5rem;
                align-items: start;
                font-weight: 400 !important;

                @media screen and (max-width: 1199px) {
                  padding: 0.875rem 1rem;
                }
              }
            }
          }
        }
      }
    }

    .navbar-toggler {
      background-color: transparent;
      margin-left: 0;
      padding: 0;
      position: relative;
      width: 24px;
      height: 18px;

      &:focus {
        box-shadow: none;
      }

      @media (max-width: 1199px) {
        margin-right: 13px;
      }

      @media (max-width: 767px) {
        margin-right: 13px;
      }

      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 24px;
        background-color: $white;
        height: 2px;
        transition: all ease-in-out 0.3s;
      }

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 24px;
        background-color: $white;
        height: 2px;
        transition: all ease-in-out 0.3s;
      }

      .navbar-toggler-icon {
        background-image: none;
        height: 2px;
        background-color: $white;
        width: 24px;
        transition: all ease-in-out 0.3s;
        display: flex;
      }
    }

    .common_dropdown {
      &.dropdown {
        .dropdown-toggle {
          padding: 0.5rem 0.2rem !important;
          color: $white;
          border: 0;
          border-radius: 0.625rem;
          font-size: 1.25rem;
          padding: 0;
          display: flex;
          align-items: center;

          @media (max-width: 991px) {
            font-size: 1.125rem;
          }

          &::after {
            display: none;
          }

          &.show {
            svg {
              path {
                fill: $baseclr;
              }
            }
          }
        }

        .dropdown-menu {
          border-radius: 0.625rem;
          border: 1px solid rgba(255, 255, 255, 0.3);
          min-width: 200px;
          position: absolute;
          top: 45px;

          @media screen and (max-width: 1199px) {
            position: static;
            padding: 0;
            min-width: 100%;
          }

          .dropdown-item {
            font-size: 1.125rem;
            font-weight: 600;
            padding: 0.625rem 1rem;
            color: $white;

            @media (max-width: 991px) {
              font-size: 1rem;
            }

            svg,
            img {
              margin-right: 10px;
            }

            &:hover,
            &.active,
            &:focus {
              background: $clr283f67;
            }

            &.white_stroke_icon {
              font-weight: 700;
              background: $gradientbluebg;
              color: $clr00b9ff;
              transition: none;

              svg {
                path {
                  fill: $clr00b9ff;
                }
              }

              &:hover,
              &.active {
                background: $clr283f67 !important;
              }
            }
          }

          &.show {

            svg,
            img {
              width: 18px;
            }
          }
        }
      }
    }

    @media screen and (max-width: 1199px) {
      .openmenuSidebar {
        border-bottom: 1px solid rgba(255, 255, 255, 0.5);
        padding: 30px 15px;

        .brandLogo {
          padding: 0;

          img {
            max-width: 150px;
          }
        }

        .navbar-toggler {
          position: absolute;
          right: 15px;
        }
      }
    }
  }


  &.openmenu {
    .navbar {
      .navbar-toggler {
        &::after {
          transform: rotate(45deg) translate(-5px, -5px);
          background-color: $white;
        }

        &::before {
          transform: rotate(-45deg) translate(-5px, 5px);
          background-color: $white;
        }

        .navbar-toggler-icon {
          opacity: 0;
        }
      }
    }
  }

  .user_icon {
    img {
      width: 26px;
      height: 26px;
    }

  }

  .sidebar_backdrop {
    @media screen and (max-width: 767px) {
      display: none;
    }
  }
}

.languageDropdown {
  width: 64px;

  @media (max-width: 1199px) {
    width: 100%;
  }

  .common_dropdown {
    @media (max-width: 1199px) {
      width: 100%;
    }

    .nav-link:hover,
    .nav-link.active,
    .nav-link:focus {
      color: #fff;
      background-color: #283f67 !important;
    }

    &.dropdown {
      .dropdown-toggle {
        color: $white;
        border: 0;
        border-radius: 0 !important;
        font-size: 1.25rem;
        padding: 0;
        display: flex;
        align-items: center;

        @media (max-width: 991px) {
          font-size: 1rem;
        }

        svg {
          margin-right: 10px;
        }

        &:focus,
        &:hover {
          background-color: transparent !important;
        }

        @media (max-width: 1199px) {
          width: 100%;
        }
      }
    }
  }

  .globalIcon .icon {
    transition: opacity 0.3s ease;
  }

  .globalIcon .blue {
    display: none;
  }

  .nav-item:hover .globalIcon .black,
  .nav-item.show .globalIcon .black {
    display: none;
  }

  .nav-item:hover .globalIcon .blue,
  .nav-item.show .globalIcon .blue {
    display: block;
  }
}

.userDropdown {
  &.common_dropdown {
    &.dropdown {
      .dropdown-toggle {
        // width: 40px;
        // height: 40px;
        // justify-content: center;
        // border-radius: 0 !important;

        .user_name {
          display: none;

          @media screen and (max-width: 1199px) {
            display: block;
            padding-left: 10px;
            font-size: 18px;

            svg {
              width: 26px;
              height: 26px;
            }
          }
        }

        @media screen and (max-width: 1199px) {
          border-bottom: 0 !important;
        }

        &:hover {
          background-color: transparent !important;
        }
      }
    }
  }
}

.brandLogo {
  @media (max-width: 1199px) {
    display: flex;
  }

  img {
    @media (max-width: 1199px) {
      max-width: 150px;
    }

    @media (max-width: 767px) {
      max-width: 110px;
    }

    @media (max-width: 359px) {
      max-width: 100px;
    }
  }
}

.sidebar_backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  // backdrop-filter: blur(1px);
  z-index: 1000;
  background-color: rgba(0, 0, 0, 0.2);
  transition: all ease-in-out 0.2s;
}

.image_color_to_white {
  filter: brightness(0) invert(1);
}

.nav-link {

  &:hover,
  &.active,
  &:focus {
    @media (min-width: 1200px) {
      background-color: $clr2A2E39 !important;
      color: $white;
    }
  }
}