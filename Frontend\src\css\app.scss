@use "./theme/var";

body {
  font-size: 1rem;
  overflow-x: clip;
  background-color: var.$themeclr;
  font-family: var(--font-gilroy);
}

*,
::after,
::before {
  box-sizing: border-box;
}

div.__toast,
.react-hot-toast,
[data-sonner-toast],
[data-toast] {
  z-index: 10000 !important;
}

hr {
  color: var.$white;
}

img {
  max-width: 100%;
}

ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

a,
a:hover {
  text-decoration: none;
  transition: all ease-in-out 0.3s;
  color: var.$baseclr;
}

.text-link {
  color: var.$baseclr;

  &:hover {
    opacity: 0.6;
  }
}

a,
span {
  display: inline-block;
}

svg {
  path {
    transition: all ease-in-out 0.3s;
  }
}

ol {
  padding: 0;
  margin: 0;
}

small {
  font-family: var.$basefont;
}

h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6,
p {
  margin-bottom: 0;
  color: var.$white;
}

h1,
.h1 {
  font-size: 3rem;
  font-weight: 800;

  // @media (max-width:1279px) {
  //   font-size: 3rem;
  // }

  @media (max-width: 1199px) {
    font-size: 2.5rem;
  }

  @media (max-width: 767px) {
    font-size: 1.5rem;
  }

  @media (max-width: 390px) {
    font-size: 1.30rem;
  }
}

h2,
.h2 {
  font-size: 5rem;
  font-weight: 800;

  @media (max-width: 1269px) {
    font-size: 3rem;
  }

  @media (max-width: 767px) {
    font-size: 1.363rem;
  }
}

h3,
.h3 {
  font-size: 2.8rem;
  font-weight: 800;

  @media (max-width: 1199px) {
    font-size: 1.688rem;
  }

  @media (max-width: 767px) {
    font-size: 1.25rem;
  }
}

h4,
.h4 {
  font-size: 1.65rem;
  line-height: 35px;
  font-weight: 600;

  @media (max-width: 767px) {
    font-size: 1.15rem;
    line-height: 25px;
  }
}

h5,
.h5 {
  font-size: 1.25rem;
  line-height: 30px;
  font-weight: 600;

  @media (max-width: 767px) {
    font-size: 1rem;
    line-height: 25px;
  }
}

h6,
.h6 {
  font-size: 1.125rem;
  font-weight: 600;

  @media (max-width: 767px) {
    font-size: 1rem;
  }
}

p {
  font-size: 1rem;
  font-weight: 400;

  @media (max-width: 767px) {
    font-size: 0.875rem;
  }

}

.font-weight-400 {
  font-weight: 400;
}

.font-weight-500 {
  font-weight: 500;
}

.font-weight-600 {
  font-weight: 600;
}

.font-weight-700 {
  font-weight: 700;
}

.font-weight-800 {
  font-weight: 800;
}

.divider {
  height: 1px;
  width: 100%;
  background-color: var.$borderclr;
  opacity: 1;
  margin: 1.25rem 0;
}

.pt-40 {
  padding-top: 40px;
}

.pt-50 {
  padding-top: 50px;
}

.pt-70 {
  padding-top: 70px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pb-70 {
  padding-bottom: 70px;
}

.py-40 {
  padding: 40px 0;
}

.py-80 {
  padding: 80px 0;

  @media (max-width: 767px) {
    padding: 40px 0;
  }
}

.py-100 {
  padding: 100px 0;

  @media (max-width: 1199px) {
    padding: 70px 0 !important;
  }

  @media (max-width: 767px) {
    padding: 50px 0 !important;
  }
}

.mt-10 {
  margin-top: 10px !important;
}

.mt-20 {
  margin-top: 20px !important;
}

.mt-30 {
  margin-top: 30px !important;
}

.mt-40 {
  margin-top: 40px !important;

  @media (max-width: 767px) {
    margin-top: 30px !important;
  }
}

.mt-50 {
  margin-top: 50px !important;

  @media (max-width: 767px) {
    margin-top: 30px !important;
  }
}

.my-10 {
  margin: 10px 0 !important;
}

.my-20 {
  margin: 20px 0 !important;
}

.my-30 {
  margin: 30px 0 !important;
}

.my-40 {
  margin: 40px 0 !important;

  @media (max-width: 767px) {
    margin: 30px 0 !important;
  }
}

.my-50 {
  margin: 50px 0 !important;

  @media (max-width: 767px) {
    margin: 30px 0 !important;
  }
}

figure {
  margin-bottom: 0;
}

.black_text {
  color: var.$black;
}

.white_text {
  color: #fff;
}

.red_text {
  color: var.$red !important;

  svg {
    path {
      fill: var.$red !important;
    }
  }
}

.green_text {
  color: var.$green !important;

  svg {
    path {
      fill: var.$green !important;
    }
  }
}

.gray_text {
  color: var.$greytext !important;

  svg {
    path {
      fill: var.$greytext !important;
    }
  }
}

.white_icon {
  svg {
    path {
      fill: var.$white !important;
    }
  }
}

.yellowlight_text {
  color: var.$yellowlighttext !important;
}

.greenlight_text {
  color: var.$greenlighttext !important;
}

.redlight_text {
  color: var.$redlighttext !important;
}

.darkblue_text {
  color: var.$clr04498C !important;
}

.blue_text {
  color: var.$baseclr !important;
}

.grey_text {
  color: var.$textclr !important;
}

.lightgrey_text {
  color: var.$lightgreyclr !important;
}

.darkgrey_text {
  color: var.$darkgreytext !important;
}

.yellow_text {
  color: var.$yellow !important;

  svg {
    path {
      fill: var.$yellow !important;
    }
  }
}

.green_bg {
  background-color: var.$greenlightbg !important;
}

.red_bg {
  background-color: var.$redlightclr !important;
}

.blue_bg {
  background-color: var.$clr031940 !important;
}

.baseblue_bg {
  background-color: #3791d3 !important;
  border-radius: 1.25rem !important;

  .solidArrow {
    svg {
      path {
        fill: var.$baseclr !important;
      }
    }
  }

  &:hover {
    background: linear-gradient(0deg, #ffffff28, #ffffff28),
      linear-gradient(270.33deg, #00adef66, #00adef26 45.5%, #00adef66 98%) !important;
  }
}

.bluelight_bg {
  background-color: var.$clr04498C !important;
  color: var.$white !important;
}

.greenlight_bg {
  background-color: var.$greenlightbg !important;
}

.white_bg {
  background-color: var.$white !important;
}

.whitelight_bg {
  background-color: rgba(255, 255, 255, 0.2) !important;
}

.Redgrandient {
  background: linear-gradient(0deg,
      rgba(255, 255, 255, 0.1),
      rgba(255, 255, 255, 0.1)),
    linear-gradient(270.33deg,
      rgba(255, 105, 106, 0.4) 0%,
      rgba(255, 105, 106, 0.2) 50%,
      rgba(255, 105, 106, 0.4) 100%) !important;

  svg {
    path {
      fill: var.$redlightclr !important;
    }
  }

  &:hover {
    background: linear-gradient(0deg, #ffffff28, #ffffff28),
      linear-gradient(270.33deg, #ff696a80, #ff696a40, #ff696a80) !important;
  }
}

.greengrandient {
  background: linear-gradient(0deg,
      rgba(255, 255, 255, 0.1),
      rgba(255, 255, 255, 0.1)),
    linear-gradient(270.33deg,
      rgba(50, 205, 51, 0.4) 0%,
      rgba(50, 205, 51, 0.15) 45.5%,
      rgba(50, 205, 51, 0.4) 98%) !important;

  svg {
    path {
      fill: var.$green !important;
    }
  }

  &:hover {
    background: linear-gradient(0deg, #ffffff28, #ffffff28),
      linear-gradient(270.33deg, #32cd3380, #32cd3340 45.5%, #32cd3380 98%) !important;
  }
}

.bluegrandient {
  background: linear-gradient(0deg,
      rgba(255, 255, 255, 0.1),
      rgba(255, 255, 255, 0.1)),
    linear-gradient(270.33deg,
      rgba(0, 173, 239, 0.4) 0%,
      rgba(0, 173, 239, 0.15) 45.5%,
      rgba(0, 173, 239, 0.4) 98%) !important;
  border-radius: 1.25rem !important;

  .solidArrow {
    svg {
      path {
        fill: var.$baseclr !important;
      }
    }
  }

  &:hover {
    background: linear-gradient(0deg, #ffffff28, #ffffff28),
      linear-gradient(270.33deg, #00adef66, #00adef26 45.5%, #00adef66 98%) !important;
  }
}

.greengrandientbg {
  border-radius: 1.25rem !important;
  background: linear-gradient(0deg,
      rgba(255, 255, 255, 0.1),
      rgba(255, 255, 255, 0.1)),
    linear-gradient(270.33deg,
      rgba(50, 205, 51, 0.4) 0%,
      rgba(50, 205, 51, 0.15) 45.5%,
      rgba(50, 205, 51, 0.4) 98%) !important;
  border: 0 !important;
}

.redgrandientbg {
  border-radius: 1.25rem !important;
  background: linear-gradient(0deg,
      rgba(255, 255, 255, 0.1),
      rgba(255, 255, 255, 0.1)),
    linear-gradient(270.33deg,
      rgba(255, 105, 106, 0.4) 0%,
      rgba(255, 105, 106, 0.2) 50%,
      rgba(255, 105, 106, 0.4) 100%) !important;
  border: 0 !important;
}

.bluedark_bg {
  background: var.$clr04498C !important;
}

.cardgrandient {
  background: radial-gradient(50% 50% at 50% 50%,
      rgba(0, 185, 255, 0.5) 21.5%,
      rgba(0, 83, 153, 0.5) 100%),
    linear-gradient(135deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.2) 47.5%,
      rgba(255, 255, 255, 0) 100%);
}

.green_arrow {
  svg {
    path {
      fill: var.$green !important;
    }
  }
}

body {
  ::-webkit-scrollbar {
    width: 5px;
    height: 4px;
    border-radius: 1rem;
  }

  ::-webkit-scrollbar-track {
    box-shadow: none;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #00adef;
    border-radius: 1rem;
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding-left: 20px !important;
  padding-right: 20px !important;

  @media (min-width: 1400px) {
    max-width: 1300px;
  }

  @media (max-width: 767px) {
    padding-left: 15px;
    padding-right: 15px;
  }
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  transition: background-color 5000s ease-in-out 0s;
  caret-color: transparent !important;
  -webkit-text-fill-color: var.$black;
}

.commonCard {
  background-color: var.$cardbg;
  padding: 2.5em;
  border-radius: 0.625rem;
  border: 1px solid var.$baseclr;

  @media (max-width: 1399px) {
    padding: 2em;
  }

  @media (max-width: 1199px) {
    padding: 1.25em 1rem;
  }
}

.borderTabs {
  white-space: nowrap;
  flex-wrap: nowrap;
  overflow-x: auto;
  overflow-y: clip;
  border-bottom: 0;

  &.nav {
    border-bottom: 1px solid var.$baseclr;

    .nav-item {

      .nav-link {
        color: var.$baseclr;
        font-size: 1rem;
        font-weight: 600;
        line-height: normal;
        background-color: transparent;
        border: 0;
        border-bottom: 3px solid transparent;
        border-radius: 0;
        padding: 0 1.5rem 1rem 1.5rem;
        transition: all ease-in-out 0.3s;

        @media (max-width: 767px) {
          font-size: 1rem;
          // padding: 0rem 0rem 1rem;
        }

        &.active {
          border-bottom: 5px solid var.$baseclr;
          color: #fff;
        }
      }
    }
  }
}

.radioBtn {
  display: flex;
  flex-wrap: wrap;

  .checkbox_input {
    .form-check {
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid var.$borderclr;
      border-radius: 1rem;
      min-height: 64px;
      padding: 0.5rem 1.25rem;
      cursor: pointer;
      transition: all ease-in-out 0.3s;

      @media (max-width: 1399px) {
        min-height: 50px;
      }

      @media (max-width: 767px) {
        padding: 0.5rem 1rem;
      }

      .form-check-input {
        margin: 0;
        cursor: pointer;
        width: 20px !important;
        height: 20px !important;
        background-color: transparent;
      }

      .form-check-label {
        margin-bottom: 0;
        cursor: pointer;
        display: flex;
        align-items: center;
        padding-left: 1.25rem;
        margin: 0;
        color: var.$greytext;

        @media (max-width: 575px) {
          padding-left: 0.5rem;
        }

        .radioIcon {
          margin-right: 0.625rem;
        }
      }

      &.active,
      &:hover {
        background-color: var.$blackbg;
        border-color: var.$baseclr;

        .form-check-label {
          color: var.$textclr;
        }
      }
    }
  }
}

.big_tabs {
  &.nav {
    .nav-item {
      display: flex;

      .nav-link {
        padding: 1.5rem 1rem;
        background-color: rgba(4, 73, 140, 0.2);
        border: 2px solid rgba(4, 73, 140, 0.2);
        width: 100%;
        border-radius: 30px;
        text-align: center;
        font-size: 24px;
        font-weight: 700;
        letter-spacing: -1px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        color: var.$white;

        @media (max-width: 767px) {
          font-size: 18px;
          padding: 1rem 0.5rem;
        }

        .tabs_icon {
          display: block;
          margin-bottom: 14px;
          width: 56px;
          height: 56px;
          background-color: rgba(3, 25, 64, 0.3);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 14px;
        }

        &.active,
        &:hover,
        &:focus {
          background: linear-gradient(180deg, #04498c 0%, #011426 100%) !important;
          border: 2px solid rgba(0, 173, 239, 0.5);
          color: var.$white;
        }
      }
    }
  }
}

.slider-container {
  .slick-slider {
    // .slick-list {
    //   .slick-track {
    //     .slick-slide {
    //     }
    //   }
    // }

    .slick-arrow {

      &.slick-prev,
      &.slick-next {
        position: absolute;
        bottom: 0px;
        width: 50px;
        height: 50px;
        border-radius: 10rem;
        background-color: var.$baseclr;
        z-index: 2;

        @media (max-width: 991px) {
          width: 32px;
          height: 32px;
        }
      }

      &.slick-prev {
        left: -25px;

        @media (min-width: 576px) and (max-width: 767px) {
          left: 5px;
        }

        @media (max-width: 374px) {
          left: 5px;
        }

        &:before {
          content: "";
          width: 8px;
          height: 15px;
          background-image: url("https: //cdn.tradereply.com/dev/site-assets/icons/tradereply-right-arrow.svg");
          background-repeat: no-repeat;
          background-size: 100%;
          position: absolute;
          top: 50%;
          left: 48%;
          transform: translate(-50%, -50%) rotate(180deg);
          opacity: 1;
        }
      }

      &.slick-next {
        right: -25px;

        @media (min-width: 576px) and (max-width: 767px) {
          right: 5px;
        }

        @media (max-width: 374px) {
          right: 5px;
        }

        &:before {
          content: "";
          width: 8px;
          height: 15px;
          background-image: url("https: //cdn.tradereply.com/dev/site-assets/icons/tradereply-right-arrow.svg");
          background-repeat: no-repeat;
          background-size: 100%;
          position: absolute;
          top: 54%;
          left: 52%;
          transform: translate(-50%, -50%);
          opacity: 1;

          // @media (max-width: 991px) {
          //   width: 16px;
          //   height: 12px;
          // }
        }
      }
    }
  }
}

.common_dropdown {
  &.dropdown {
    .dropdown-toggle {
      color: var.$white;
      border: 0;
      border-radius: 0.625rem;
      font-size: 1.25rem;
      padding: 0.625rem 1.25rem;
      display: flex;
      align-items: center;

      @media (max-width: 991px) {
        font-size: 1.8rem;
      }

      @media (min-width: 1200px) {
        &:hover {
          background-color: var.$clr283f67;
          color: var.$white;
        }
      }
    }

    .dropdown-menu {
      background-color: #031940;
      border-radius: 0.625rem;
      border: 1px solid rgba(255, 255, 255, 0.3);
      min-width: 200px;

      .dropdown-item {
        font-size: 1.25rem;
        font-weight: 600;
        padding: 0.625rem 1rem;
        color: var.$white;

        &:hover {
          background-color: var.$clr283f67;
          color: var.$white;
        }

        @media (max-width: 991px) {
          font-size: 1rem;
        }
      }
    }
  }
}

.home-page {
  .common_dropdown {
    &.dropdown {
      .dropdown-menu {
        background-color: var.$clr1E222D;

        .dropdown-item {
          &:hover {
            background-color: var.$clr2A2E39;
            color: var.$white;
          }
        }
      }
    }
  }
}

.form-control {
  min-height: 56px;
  box-shadow: none;
  outline: none;
  width: 100%;
  padding: 0.5rem 1.25rem;
  border-radius: 1rem;
  border: 1px solid var.$inputbgclr;
  background-color: var.$inputbgclr;
  color: var.$white;
  font-size: 1rem;

  @media (max-width: 1599px) {
    min-height: 52px;
    font-size: 1rem;
  }

  &.is-invalid,
  &.was-validated,
  &:invalid {
    background-image: none;
    border-color: var.$red;
  }

  &:hover {
    appearance: none;
  }

  &::placeholder {
    color: var.$white;
    opacity: 0.4;
  }

  &:disabled {
    background-color: transparent;
  }

  &:focus {
    box-shadow: none;
    border: 1px solid var.$inputbgclr;
    background-color: var.$inputbgclr;
    color: var.$white;
  }

  &.passwordInput {
    padding-right: 4.375rem;
  }
}

.login_fontStyle_forget {
  font-weight: 700;
}

.text_area_bg {
  width: 100%;
  height: 15rem;
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background-color: rgba(255, 255, 255, 0.3);
}

.text-pre-line {
  white-space: pre-line;
}



.select-btn {
  border-radius: 7px !important;
  background: #136fcb;
  width: 50%;
  margin: 10px 0px;
}

.green_btn {
  background-color: var.$green !important;
  color: #fff;
}

.green_btn:hover {
  background-color: var.$greenbtnhover !important;
}

.white_btn {
  background-color: var.$white !important;
  color: var.$black !important;
  border: 1px solid #00000033 !important;
}

.yellow_btn {
  background-color: var.$yellow !important;
  color: #fff;
}

.password_check p {
  font-size: 14px;
  text-align: center;
}

.password_check .box1 p {
  color: #FF696A;
  font-weight: 600;
}

.password_check .box1_bg {
  background-color: #FF696A;
}

.password_check .box2_bg {
  background-color: #FF6A23;
}

.password_check .box3_bg {
  background-color: #FFA723;
}

.password_check .box4_bg {
  background-color: #FCD53F;
}

.password_check .box5_bg {
  background-color: #DFF33B;
}

.password_check .white10_bg {
  background-color: #ffffff10;
}

.security_check .user_email {
  color: #32CD33;
  font-size: 18px;
  font-weight: 600;
}

.security_check_input input {
  height: 57px;
  width: 58px;
  border-radius: 15px;
  background-color: #ffffff30;
  text-align: center;
  font-size: 30px;
}

.security_check_input input:focus-visible {
  outline: 1px solid white !important;
}

.security_check_resend_btn {
  background-color: #031940;
  padding: 10px 50px;
  border-radius: 50px;
  transition: all .3s ease-in-out;
  font-weight: 600;
  font-size: 20px;
}

.security_check_resend_btn:hover {
  background-color: #0099d1;
  color: #fff;
}

.security_check_resend_btn:disabled,
.security_check_resend_btn:disabled:hover {
  background-color: #6c757d;
  cursor: not-allowed;
}

.rotate {
  animation: spin 1s linear;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.svg-baseblue {
  filter: invert(49%) sepia(63%) saturate(5181%) hue-rotate(171deg) brightness(96%) contrast(97%);
  width: 33px;
  height: 32px;
}

.baseblue_border {
  border-color: var.$baseclr !important;
}

.darkblue_border {
  border-color: var.$clr04498C !important;
}

.darkgray_border {
  border-color: var.$darkgreytext !important;
}

.portfolio-blur-overlay {
  position: fixed;
  z-index: 99999;
  inset: 0;
  background-color: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(3px);
  display: flex;
  align-items: center;
  justify-content: center;

  .loader-content {
    text-align: center;
    font-weight: bold;
    color: #1338ff;

    .spinner-border {
      width: 2.5rem;
      height: 2.5rem;
    }
  }
}

.loading-screen {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}