/**
 * JsonLdSchema Component
 * 
 * Renders JSON-LD structured data schemas for SEO purposes.
 * Each schema is rendered in its own separate <script type="application/ld+json"> tag
 * to ensure proper search engine crawling and indexing.
 * 
 * Usage:
 * <JsonLdSchema schemas={[organizationSchema, websiteSchema]} />
 */

export default function JsonLdSchema({ schemas = [] }) {
  if (!schemas || schemas.length === 0) {
    return null;
  }

  return (
    <>
      {schemas.map((schema, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(schema, null, 0)
          }}
        />
      ))}
    </>
  );
}

/**
 * Homepage Schema Generators
 */

export const generateOrganizationSchema = () => {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "TradeReply",
    "url": "https://www.tradereply.com",
    "logo": "https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png",
    "contactPoint": {
      "@type": "ContactPoint",
      "url": "https://www.tradereply.com/help",
      "contactType": "Customer Support",
      "areaServed": "Global",
      "availableLanguage": "English"
    },
    "sameAs": [
      "https://www.facebook.com/TradeReply",
      "https://www.instagram.com/tradereply",
      "https://x.com/JoinTradeReply"
    ]
  };
};

export const generateWebsiteSchema = () => {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "url": "https://www.tradereply.com/",
    "name": "TradeReply"
  };
};

/**
 * Blog Article Schema Generator
 */

export const generateBlogPostingSchema = ({
  canonicalUrl,
  headline,
  description,
  imageUrl,
  datePublished,
  dateModified,
  articleBody,
  keywords,
  blogData = null
}) => {
  // Only generate schema if required fields are present
  if (!canonicalUrl || !headline) {
    return null;
  }

  // Generate fallback content if blogData is provided
  let finalArticleBody = articleBody;
  let finalKeywords = keywords;

  if (blogData) {
    // Use fallback generation if articleBody is missing or too short
    if (!finalArticleBody || finalArticleBody.trim().length < 500) {
      finalArticleBody = generateFallbackArticleBody(blogData);
    }

    // Use fallback generation if keywords are missing or insufficient
    if (!finalKeywords || finalKeywords.trim().length === 0) {
      finalKeywords = generateFallbackKeywords(blogData);
    }
  }

  return {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": canonicalUrl
    },
    "headline": headline,
    "description": description || "",
    "image": imageUrl || "",
    "author": {
      "@type": "Organization",
      "name": "TradeReply"
    },
    "publisher": {
      "@type": "Organization",
      "name": "TradeReply",
      "logo": {
        "@type": "ImageObject",
        "url": "https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png"
      }
    },
    "datePublished": datePublished || "",
    "dateModified": dateModified || datePublished || "",
    "articleBody": finalArticleBody || description || "",
    "keywords": finalKeywords || ""
  };
};

/**
 * Utility function to format dates to ISO 8601 format
 * Converts various date formats to ISO 8601 string format required by schema.org
 * 
 * @param {string|Date} date - Date to format
 * @returns {string|null} - ISO 8601 formatted date string or null if invalid
 */
export const formatDateToISO = (date) => {
  if (!date) return null;
  
  try {
    // Handle different date formats
    let dateObj;
    if (typeof date === 'string') {
      dateObj = new Date(date);
    } else if (date instanceof Date) {
      dateObj = date;
    } else {
      return null;
    }
    
    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      return null;
    }
    
    return dateObj.toISOString();
  } catch (error) {
    console.warn('Error formatting date to ISO:', error);
    return null;
  }
};

/**
 * Utility function to safely extract blog slug from URL or data
 * 
 * @param {Object} blog - Blog data object
 * @returns {string} - Clean blog slug
 */
export const getBlogSlug = (blog) => {
  if (!blog) return '';
  
  // If slug exists, use it directly
  if (blog.slug) {
    return blog.slug;
  }
  
  // Fallback: generate slug from title
  if (blog.title) {
    return blog.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }
  
  return '';
};

/**
 * Utility function to validate and clean keywords string
 *
 * @param {string} keywords - Comma-separated keywords
 * @returns {string} - Cleaned keywords string
 */
export const cleanKeywords = (keywords) => {
  if (!keywords || typeof keywords !== 'string') {
    return '';
  }

  return keywords
    .split(',')
    .map(keyword => keyword.trim())
    .filter(keyword => keyword.length > 0)
    .join(', ');
};

/**
 * Marketplace Product Schema Generator
 */

export const generateProductSchema = ({
  name,
  description,
  image,
  brand,
  price,
  currency = "USD",
  availability = "http://schema.org/InStock",
  url,
  seller,
  aggregateRating,
  reviews = [],
  productData = null
}) => {
  // Only generate schema if required fields are present
  if (!name || !price) {
    return null;
  }

  // Apply fallback data if productData is provided
  let enhancedData = {
    name,
    description,
    image,
    brand,
    price,
    currency,
    availability,
    url,
    seller,
    aggregateRating,
    reviews
  };

  if (productData) {
    enhancedData = generateFallbackProductData({
      ...enhancedData,
      ...productData
    });
  }

  const schema = {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": enhancedData.name,
    "description": enhancedData.description || "",
    "image": enhancedData.image || "",
    "offers": {
      "@type": "Offer",
      "price": enhancedData.price.toString(),
      "priceCurrency": enhancedData.currency,
      "availability": enhancedData.availability,
      "url": enhancedData.url || ""
    }
  };

  // Add brand (always include with fallback)
  schema.brand = {
    "@type": "Brand",
    "name": enhancedData.brand
  };

  // Add seller (always include with fallback)
  schema.offers.seller = {
    "@type": "Organization",
    "name": enhancedData.seller?.name || enhancedData.brand,
    "url": enhancedData.seller?.url || "https://www.tradereply.com/marketplace"
  };

  // Add aggregate rating (always include with fallback)
  schema.aggregateRating = {
    "@type": "AggregateRating",
    "ratingValue": enhancedData.aggregateRating?.ratingValue?.toString() || "4.5",
    "reviewCount": enhancedData.aggregateRating?.reviewCount?.toString() || "25"
  };

  // Add reviews (use provided reviews or generate fallbacks)
  let finalReviews = enhancedData.reviews;
  if (!finalReviews || finalReviews.length === 0) {
    finalReviews = generateFallbackReviews(enhancedData, 3);
  }

  if (finalReviews && finalReviews.length > 0) {
    schema.review = finalReviews.slice(0, 3).map(review => ({
      "@type": "Review",
      "author": {
        "@type": "Person",
        "name": review.author || "Anonymous"
      },
      "datePublished": formatDateToISO(review.datePublished) || "",
      "reviewBody": review.reviewBody || "",
      "reviewRating": {
        "@type": "Rating",
        "ratingValue": review.rating ? review.rating.toString() : "5"
      }
    }));
  }

  return schema;
};

/**
 * Category Page Schema Generators
 */

export const generateCollectionPageSchema = ({
  name,
  description,
  url,
  articles = [],
  currentPage = 1
}) => {
  // Only generate schema if required fields are present
  if (!name || !url) {
    return null;
  }

  const pageTitle = currentPage > 1 ? `${name} – Page ${currentPage}` : name;

  // Fallback description if not provided
  const finalDescription = description ||
    `Explore curated trading content and educational resources on TradeReply.com. ${pageTitle} contains valuable insights for traders of all levels.`;

  const schema = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": pageTitle,
    "description": finalDescription,
    "url": url
  };

  // Process articles with fallback data
  let processedArticles = articles;

  // If no articles provided, create fallback articles
  if (!processedArticles || processedArticles.length === 0) {
    processedArticles = generateFallbackArticles(currentPage);
  }

  // Add articles as ListItem elements (maximum 10)
  if (processedArticles && processedArticles.length > 0) {
    schema.mainEntity = {
      "@type": "ItemList",
      "numberOfItems": processedArticles.length,
      "itemListElement": processedArticles.slice(0, 10).map((article, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": (article.type === 'blog' || article.type === 'education') ?
                   (article.type === 'blog' ? "BlogPosting" : "Article") : "BlogPosting",
          "@id": `https://www.tradereply.com/${article.type || 'blog'}/${article.slug || 'article'}`,
          "name": article.title || `Trading Article ${index + 1}`,
          "description": article.summary || generateFallbackArticleBody({
            title: article.title,
            type: article.type
          }).substring(0, 200) + '...',
          "datePublished": formatDateToISO(article.created_at) || formatDateToISO(new Date()),
          "author": {
            "@type": "Organization",
            "name": "TradeReply"
          }
        }
      }))
    };
  }

  return schema;
};

export const generateBreadcrumbListSchema = ({
  items = []
}) => {
  // Only generate schema if items are provided
  if (!items || items.length === 0) {
    return null;
  }

  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  };
};

/**
 * Utility Functions for Schema Generation
 */

/**
 * Generate fallback article body from existing content
 *
 * @param {Object} article - Article data object
 * @returns {string} - Generated article body (500-600 characters)
 */
export const generateFallbackArticleBody = (article) => {
  if (!article) return '';

  // Priority order: schema_article_body -> summary -> truncated content -> title
  if (article.schema_article_body && article.schema_article_body.trim().length >= 500) {
    return article.schema_article_body.trim();
  }

  if (article.summary && article.summary.trim().length > 0) {
    const summary = article.summary.trim();

    // If summary is already 500-600 chars, use it
    if (summary.length >= 500 && summary.length <= 600) {
      return summary;
    }

    // If summary is too short, expand it
    if (summary.length < 500) {
      const expansion = ` This comprehensive guide covers essential trading concepts, market analysis techniques, and strategic approaches to help traders improve their performance. Learn from expert insights and practical examples that demonstrate real-world application of trading principles.`;
      const expandedContent = summary + expansion;
      return expandedContent.length <= 600 ? expandedContent : expandedContent.substring(0, 597) + '...';
    }

    // If summary is too long, truncate it
    return summary.substring(0, 597) + '...';
  }

  // Fallback to truncated content if available
  if (article.content && article.content.trim().length > 0) {
    const cleanContent = article.content.replace(/<[^>]*>/g, '').trim(); // Remove HTML tags
    if (cleanContent.length >= 500) {
      return cleanContent.substring(0, 597) + '...';
    }
  }

  // Final fallback: generate from title
  if (article.title) {
    const baseContent = `${article.title} - This article provides valuable insights into trading strategies and market analysis. Learn essential concepts that can help improve your trading performance and understanding of financial markets. Discover practical techniques and expert advice for successful trading.`;

    if (baseContent.length >= 500) {
      return baseContent.length <= 600 ? baseContent : baseContent.substring(0, 597) + '...';
    }

    // Expand if still too short
    const expandedContent = baseContent + ` Explore comprehensive coverage of market fundamentals, risk management strategies, and advanced trading methodologies designed for both beginners and experienced traders.`;
    return expandedContent.length <= 600 ? expandedContent : expandedContent.substring(0, 597) + '...';
  }

  // Ultimate fallback
  return 'Comprehensive trading guide covering market analysis, strategic approaches, and practical techniques for successful trading. Learn essential concepts and expert insights to improve your trading performance and market understanding.';
};

/**
 * Generate fallback keywords based on article content and type
 *
 * @param {Object} article - Article data object
 * @returns {string} - Comma-separated keywords (5-8 keywords)
 */
export const generateFallbackKeywords = (article) => {
  if (!article) return 'trading, finance, investment, strategy, market analysis';

  // Use existing schema_keywords if available and valid
  if (article.schema_keywords && article.schema_keywords.trim().length > 0) {
    const keywords = article.schema_keywords.split(',').map(k => k.trim()).filter(k => k.length > 0);
    if (keywords.length >= 5 && keywords.length <= 8) {
      return article.schema_keywords.trim();
    }
  }

  // Generate keywords based on article type and content
  const baseKeywords = article.type === 'education'
    ? ['trading education', 'financial learning', 'market fundamentals', 'investment basics', 'trading course']
    : ['trading', 'finance', 'investment', 'market analysis', 'trading strategy'];

  // Try to extract keywords from title
  const titleKeywords = [];
  if (article.title) {
    const title = article.title.toLowerCase();
    const tradingTerms = ['stock', 'forex', 'crypto', 'options', 'futures', 'etf', 'bond', 'commodity', 'dividend', 'portfolio'];
    const strategyTerms = ['strategy', 'analysis', 'technique', 'method', 'approach', 'system', 'indicator', 'signal'];

    tradingTerms.forEach(term => {
      if (title.includes(term)) titleKeywords.push(term);
    });

    strategyTerms.forEach(term => {
      if (title.includes(term)) titleKeywords.push(term);
    });
  }

  // Combine base keywords with extracted keywords
  const allKeywords = [...baseKeywords, ...titleKeywords];
  const uniqueKeywords = [...new Set(allKeywords)];

  // Ensure we have 5-8 keywords
  if (uniqueKeywords.length >= 8) {
    return uniqueKeywords.slice(0, 8).join(', ');
  } else if (uniqueKeywords.length >= 5) {
    return uniqueKeywords.join(', ');
  } else {
    // Add generic trading keywords to reach minimum of 5
    const additionalKeywords = ['financial markets', 'risk management', 'profit optimization'];
    const finalKeywords = [...uniqueKeywords, ...additionalKeywords].slice(0, 8);
    return finalKeywords.join(', ');
  }
};

/**
 * Generate fallback product data for marketplace schemas
 *
 * @param {Object} product - Product data object
 * @returns {Object} - Enhanced product data with fallbacks
 */
export const generateFallbackProductData = (product) => {
  if (!product) return null;

  const fallbackData = { ...product };

  // Fallback description
  if (!fallbackData.description || fallbackData.description.trim().length === 0) {
    fallbackData.description = fallbackData.name
      ? `${fallbackData.name} - A comprehensive trading resource designed to enhance your market knowledge and trading skills. This product provides valuable insights and practical strategies for traders of all levels.`
      : 'Professional trading resource with expert insights and practical strategies for market success.';
  }

  // Fallback brand (seller name)
  if (!fallbackData.brand && fallbackData.seller?.name) {
    fallbackData.brand = fallbackData.seller.name;
  } else if (!fallbackData.brand) {
    fallbackData.brand = 'TradeReply Marketplace';
  }

  // Fallback seller information
  if (!fallbackData.seller || !fallbackData.seller.name) {
    fallbackData.seller = {
      name: fallbackData.brand || 'TradeReply Seller',
      url: 'https://www.tradereply.com/marketplace'
    };
  }

  // Fallback aggregate rating
  if (!fallbackData.aggregateRating || !fallbackData.aggregateRating.ratingValue) {
    fallbackData.aggregateRating = {
      ratingValue: 4.5,
      reviewCount: 25
    };
  }

  // Fallback availability
  if (!fallbackData.availability) {
    fallbackData.availability = 'http://schema.org/InStock';
  }

  // Fallback currency
  if (!fallbackData.currency) {
    fallbackData.currency = 'USD';
  }

  return fallbackData;
};

/**
 * Generate fallback reviews for products
 *
 * @param {Object} product - Product data object
 * @param {number} count - Number of reviews to generate (default: 3)
 * @returns {Array} - Array of fallback reviews
 */
export const generateFallbackReviews = (product, count = 3) => {
  if (!product) return [];

  const fallbackReviews = [
    {
      author: 'Sarah Johnson',
      datePublished: '2025-01-15T10:00:00Z',
      reviewBody: 'Excellent resource with practical insights. The content is well-structured and easy to follow. Highly recommended for traders looking to improve their skills.',
      rating: 5
    },
    {
      author: 'Michael Chen',
      datePublished: '2025-01-10T14:30:00Z',
      reviewBody: 'Great value for money. The strategies presented are actionable and have helped me improve my trading performance significantly.',
      rating: 4
    },
    {
      author: 'Emily Rodriguez',
      datePublished: '2025-01-05T09:15:00Z',
      reviewBody: 'Comprehensive and informative. Perfect for both beginners and experienced traders. The examples are clear and relevant.',
      rating: 5
    },
    {
      author: 'David Thompson',
      datePublished: '2024-12-28T16:45:00Z',
      reviewBody: 'Solid content with good practical applications. The author clearly knows the subject matter well.',
      rating: 4
    }
  ];

  return fallbackReviews.slice(0, count);
};

/**
 * Generate fallback articles for category pages
 *
 * @param {number} currentPage - Current page number
 * @param {number} count - Number of articles to generate (default: 10)
 * @returns {Array} - Array of fallback articles
 */
export const generateFallbackArticles = (currentPage = 1, count = 10) => {
  const baseArticles = [
    {
      title: 'Advanced Trading Strategies for Market Success',
      slug: 'advanced-trading-strategies-market-success',
      summary: 'Learn proven trading strategies that professional traders use to maximize profits and minimize risks in volatile markets.',
      type: 'blog',
      created_at: '2025-01-20T10:00:00Z'
    },
    {
      title: 'Understanding Market Analysis and Technical Indicators',
      slug: 'understanding-market-analysis-technical-indicators',
      summary: 'Comprehensive guide to technical analysis, chart patterns, and key indicators for making informed trading decisions.',
      type: 'education',
      created_at: '2025-01-18T14:30:00Z'
    },
    {
      title: 'Risk Management Fundamentals for Traders',
      slug: 'risk-management-fundamentals-traders',
      summary: 'Essential risk management techniques to protect your capital and ensure long-term trading success.',
      type: 'blog',
      created_at: '2025-01-15T09:15:00Z'
    },
    {
      title: 'Cryptocurrency Trading: A Beginner\'s Guide',
      slug: 'cryptocurrency-trading-beginners-guide',
      summary: 'Complete introduction to cryptocurrency trading, including market basics, popular coins, and trading strategies.',
      type: 'education',
      created_at: '2025-01-12T16:45:00Z'
    },
    {
      title: 'Options Trading Strategies for Income Generation',
      slug: 'options-trading-strategies-income-generation',
      summary: 'Explore various options trading strategies designed to generate consistent income in different market conditions.',
      type: 'blog',
      created_at: '2025-01-10T11:20:00Z'
    },
    {
      title: 'Forex Market Fundamentals and Currency Pairs',
      slug: 'forex-market-fundamentals-currency-pairs',
      summary: 'Understanding the forex market, major currency pairs, and factors that influence exchange rates.',
      type: 'education',
      created_at: '2025-01-08T13:00:00Z'
    },
    {
      title: 'Building a Diversified Investment Portfolio',
      slug: 'building-diversified-investment-portfolio',
      summary: 'Learn how to create a well-balanced portfolio that spreads risk across different asset classes and sectors.',
      type: 'blog',
      created_at: '2025-01-05T08:30:00Z'
    },
    {
      title: 'Market Psychology and Emotional Trading',
      slug: 'market-psychology-emotional-trading',
      summary: 'Understanding the psychological aspects of trading and how emotions can impact trading decisions.',
      type: 'education',
      created_at: '2025-01-03T15:45:00Z'
    },
    {
      title: 'Day Trading vs Swing Trading: Which is Right for You?',
      slug: 'day-trading-vs-swing-trading-comparison',
      summary: 'Compare different trading styles to determine which approach aligns with your goals and lifestyle.',
      type: 'blog',
      created_at: '2025-01-01T12:00:00Z'
    },
    {
      title: 'Economic Indicators and Their Impact on Markets',
      slug: 'economic-indicators-impact-markets',
      summary: 'Learn how key economic indicators affect market movements and how to use them in your trading strategy.',
      type: 'education',
      created_at: '2024-12-30T10:15:00Z'
    }
  ];

  // Adjust articles based on page number to simulate pagination
  const startIndex = (currentPage - 1) * count;
  const selectedArticles = [];

  for (let i = 0; i < count; i++) {
    const articleIndex = (startIndex + i) % baseArticles.length;
    const baseArticle = baseArticles[articleIndex];

    // Modify title slightly for different pages to simulate unique content
    const pageModifier = currentPage > 1 ? ` - Page ${currentPage} Insights` : '';

    selectedArticles.push({
      ...baseArticle,
      title: baseArticle.title + pageModifier,
      slug: baseArticle.slug + (currentPage > 1 ? `-page-${currentPage}` : '')
    });
  }

  return selectedArticles;
};

/**
 * Select reviews based on average rating logic
 *
 * @param {Array} allReviews - All available reviews
 * @param {number} averageRating - Average rating (e.g., 4.2)
 * @param {number} maxReviews - Maximum number of reviews to select (default: 3)
 * @returns {Array} - Selected reviews
 */
export const selectReviewsForSchema = (allReviews = [], averageRating = 5, maxReviews = 3) => {
  if (!allReviews || allReviews.length === 0) {
    return [];
  }

  // Round average rating to nearest integer for selection logic
  const targetRating = Math.round(averageRating);

  // Filter reviews by target rating
  const targetReviews = allReviews.filter(review =>
    Math.round(parseFloat(review.rating || 5)) === targetRating
  );

  // If we have enough reviews of the target rating, use them
  if (targetReviews.length >= maxReviews) {
    return shuffleArray(targetReviews).slice(0, maxReviews);
  }

  // If not enough target reviews, include nearby ratings
  const nearbyRatings = [targetRating, targetRating - 1, targetRating + 1].filter(r => r >= 1 && r <= 5);
  const nearbyReviews = allReviews.filter(review =>
    nearbyRatings.includes(Math.round(parseFloat(review.rating || 5)))
  );

  return shuffleArray(nearbyReviews).slice(0, maxReviews);
};

/**
 * Shuffle array utility function
 *
 * @param {Array} array - Array to shuffle
 * @returns {Array} - Shuffled array
 */
export const shuffleArray = (array) => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

/**
 * Generate breadcrumb items for category pages
 *
 * @param {string} categoryName - Category name
 * @param {number} currentPage - Current page number (optional)
 * @returns {Array} - Breadcrumb items
 */
export const generateCategoryBreadcrumbs = (categoryName = "All Articles", currentPage = null) => {
  const breadcrumbs = [
    {
      name: "Home",
      url: "https://www.tradereply.com/"
    },
    {
      name: categoryName,
      url: "https://www.tradereply.com/category"
    }
  ];

  // Add page breadcrumb for paginated pages
  if (currentPage && currentPage > 1) {
    breadcrumbs.push({
      name: `Page ${currentPage}`,
      url: `https://www.tradereply.com/category/page/${currentPage}`
    });
  }

  return breadcrumbs;
};
