import axios from 'axios';
import Cookies from 'js-cookie';

const axiosInstance = axios.create({
  baseURL: `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/`,
  withCredentials: true,
  headers: {
    "Accept": "application/json"
  },
});

axiosInstance.interceptors.request.use(
  (config) => {
    const token = Cookies.get("authToken"); // Fetch latest token
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response Interceptor: Handle Unauthorized Errors
axiosInstance.interceptors.response.use(
  (response) => response,  
  (error) => {
    if (error.response) {
      const { status } = error.response;

      if (status === 401) {
        console.log('Unauthorized access. Redirecting to login...');
        Cookies.remove("authToken");  // Clear auth token
        window.location.href = '/login'; // Redirect to login page
      } else if (status === 404) {
        console.error('Resource not found!');
      } else if (status >= 500) {
        console.error('Server error! Please try again later.');
      }
    } else {
      console.error('Network error or request timeout.');
    }

    return Promise.reject(error);
  }
);

export default axiosInstance;
