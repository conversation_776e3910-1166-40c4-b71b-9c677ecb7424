"use client";

import AdminHeading from "@/Components/common/Dashboard/AdminHeading";
import CommonHead from "@/Components/common/Dashboard/CommonHead";
import ConfigureTab from "@/Components/common/TradeAnalysis/ConfigureTab";
import MiniTabs from "@/Components/common/TradeAnalysis/MiniTabs";
import SetupTab from "@/Components/common/TradeAnalysis/SetupTab";
import ViewTab from "@/Components/common/TradeAnalysis/ViewTab";
import "@/css/dashboard/TradeAnalysis.scss";
import DashboardLayout from "@/Layouts/DashboardLayout";
import { useState } from "react";
import { Container } from "react-bootstrap";

const TradeAnalysisClient = () => {
  const [activeTab, setActiveTab] = useState('Setup');
  const addActiveTab = (active) => {
    setActiveTab(active)

  }

  return (
    <DashboardLayout>
      <div className="trade_manager trade_builder">
        <CommonHead isShowCalender={false} />
        <Container>
          <div className="trade_head">
            <AdminHeading heading="Trade Analysis" />
          </div>
          <MiniTabs
            onClick={addActiveTab}
            activeTab={activeTab}
          />
          {activeTab == 'Configure' && (
            <ConfigureTab />
          )}
          {activeTab == 'Setup' && (
            <SetupTab />
          )}
          {activeTab == 'View' && (
            <ViewTab />
          )}
        </Container>
      </div>
    </DashboardLayout>
  );
};

export default TradeAnalysisClient;
